"""
AI分析规则数据访问层
提供配置驱动的分析规则管理
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc

from ..database.base_repository import BaseRepository
from ..models.analysis_rule import AnalysisRule
from ..models.rule_behavior_mapping import RuleBehaviorMapping
from ..models.anomaly_behavior import AnomalyBehavior
from ..core.logger import logger


class AnalysisRuleRepository(BaseRepository[AnalysisRule]):
    """分析规则Repository - 配置驱动架构核心"""
    
    def __init__(self, db: Session):
        super().__init__(AnalysisRule, db)
    
    async def get_camera_rules(self, camera_id: int) -> List[AnalysisRule]:
        """获取摄像头的所有分析规则"""
        return await self.find_by_conditions([
            AnalysisRule.camera_id == camera_id,
            AnalysisRule.is_enabled == True,
            AnalysisRule.deleted == False
        ], order_by=[AnalysisRule.priority.desc()])
    
    async def get_store_rules(self, store_id: int) -> List[AnalysisRule]:
        """获取门店级别的分析规则"""
        return await self.find_by_conditions([
            AnalysisRule.store_id == store_id,
            AnalysisRule.is_enabled == True,
            AnalysisRule.deleted == False
        ], order_by=[AnalysisRule.priority.desc()])
    
    async def get_global_rules(self) -> List[AnalysisRule]:
        """获取全局分析规则"""
        return await self.find_by_conditions([
            AnalysisRule.store_id.is_(None),
            AnalysisRule.apply_scope == "all_cameras",
            AnalysisRule.is_enabled == True,
            AnalysisRule.deleted == False
        ], order_by=[AnalysisRule.priority.desc()])
    
    async def get_effective_rules(self, camera_id: int, store_id: int) -> List[AnalysisRule]:
        """
        获取摄像头的有效规则（配置优先级：摄像头专属 > 门店级别 > 全局默认）
        """
        try:
            effective_rules = []
            
            # 1. 摄像头专属规则（最高优先级）
            camera_rules = await self.get_camera_rules(camera_id)
            effective_rules.extend(camera_rules)
            logger.debug(f"摄像头 {camera_id} 专属规则: {len(camera_rules)} 个")
            
            # 2. 门店级别规则
            store_rules = await self.get_store_rules(store_id)
            effective_rules.extend(store_rules)
            logger.debug(f"门店 {store_id} 级别规则: {len(store_rules)} 个")
            
            # 3. 全局默认规则
            global_rules = await self.get_global_rules()
            effective_rules.extend(global_rules)
            logger.debug(f"全局默认规则: {len(global_rules)} 个")
            
            # 按优先级排序
            effective_rules.sort(key=lambda x: x.priority, reverse=True)
            
            logger.info(f"摄像头 {camera_id} 有效规则总数: {len(effective_rules)}")
            return effective_rules
            
        except Exception as e:
            logger.error(f"获取摄像头 {camera_id} 有效规则失败: {e}")
            return []
    
    async def get_rule_with_behaviors(self, rule_id: int) -> Dict[str, Any]:
        """获取规则及其关联的异常行为配置"""
        try:
            rule = await self.get_by_id(rule_id)
            if not rule:
                return {}
            
            # 获取规则关联的异常行为映射
            mappings_query = self.db.query(RuleBehaviorMapping, AnomalyBehavior).join(
                AnomalyBehavior, RuleBehaviorMapping.behavior_id == AnomalyBehavior.id
            ).filter(
                and_(
                    RuleBehaviorMapping.rule_id == rule_id,
                    RuleBehaviorMapping.is_enabled == True,
                    RuleBehaviorMapping.deleted == False,
                    AnomalyBehavior.is_enabled == True,
                    AnomalyBehavior.deleted == False
                )
            )
            
            mappings = mappings_query.all()
            
            rule_config = {
                "rule_id": rule.id,
                "rule_name": rule.name,
                "rule_type": rule.rule_type,
                "camera_id": rule.camera_id,
                "store_id": rule.store_id,
                "confidence_threshold": float(rule.confidence_threshold),
                "consecutive_frames": rule.consecutive_frames,
                "time_window_seconds": rule.time_window_seconds,
                "cooldown_seconds": rule.cooldown_seconds,
                "severity_override": rule.severity_override,
                "trigger_conditions": rule.trigger_conditions or {},
                "apply_scope": rule.apply_scope,
                "priority": rule.priority,
                "behaviors": []
            }
            
            for mapping, behavior in mappings:
                behavior_config = {
                    "mapping_id": mapping.id,
                    "behavior_id": behavior.id,
                    "behavior_code": behavior.code,
                    "behavior_name": behavior.name,
                    "category": behavior.category,
                    "description": behavior.description,
                    "ai_keywords": behavior.ai_keywords or [],
                    "default_severity": behavior.default_severity,
                    "confidence_threshold": float(
                        mapping.confidence_threshold_override or behavior.default_confidence_threshold
                    ),
                    "severity": mapping.severity_override or behavior.default_severity,
                    "weight": float(mapping.weight),
                    "custom_params": mapping.custom_params or {}
                }
                rule_config["behaviors"].append(behavior_config)
            
            return rule_config
            
        except Exception as e:
            logger.error(f"获取规则 {rule_id} 及行为配置失败: {e}")
            return {}
    
    async def create_rule_with_behaviors(self, rule_data: Dict[str, Any], 
                                       behavior_mappings: List[Dict[str, Any]]) -> Optional[int]:
        """创建规则及其行为映射"""
        try:
            # 创建规则
            rule_id = await self.create(rule_data)
            if not rule_id:
                return None
            
            # 创建行为映射
            for mapping in behavior_mappings:
                mapping["rule_id"] = rule_id
                mapping_repo = RuleBehaviorMappingRepository(self.db)
                await mapping_repo.create(mapping)
            
            logger.info(f"创建规则 {rule_id} 及 {len(behavior_mappings)} 个行为映射")
            return rule_id
            
        except Exception as e:
            logger.error(f"创建规则及行为映射失败: {e}")
            return None
    
    async def update_rule_priority(self, rule_id: int, new_priority: int) -> bool:
        """更新规则优先级"""
        try:
            success = await self.update_by_id(rule_id, {"priority": new_priority})
            if success:
                logger.info(f"规则 {rule_id} 优先级更新为: {new_priority}")
            return success
            
        except Exception as e:
            logger.error(f"更新规则 {rule_id} 优先级失败: {e}")
            return False
    
    async def get_rules_by_behavior(self, behavior_id: int) -> List[Dict[str, Any]]:
        """获取使用指定异常行为的所有规则"""
        try:
            query = self.db.query(AnalysisRule).join(
                RuleBehaviorMapping, AnalysisRule.id == RuleBehaviorMapping.rule_id
            ).filter(
                and_(
                    RuleBehaviorMapping.behavior_id == behavior_id,
                    RuleBehaviorMapping.is_enabled == True,
                    RuleBehaviorMapping.deleted == False,
                    AnalysisRule.is_enabled == True,
                    AnalysisRule.deleted == False
                )
            ).distinct()
            
            rules = query.all()
            
            return [{
                "rule_id": rule.id,
                "rule_name": rule.name,
                "camera_id": rule.camera_id,
                "store_id": rule.store_id,
                "apply_scope": rule.apply_scope,
                "priority": rule.priority
            } for rule in rules]
            
        except Exception as e:
            logger.error(f"获取使用行为 {behavior_id} 的规则失败: {e}")
            return []
    
    async def get_rules_summary(self) -> Dict[str, Any]:
        """获取规则统计摘要"""
        try:
            all_rules = await self.find_by_conditions([
                AnalysisRule.deleted == False
            ])
            
            summary = {
                "total_count": len(all_rules),
                "enabled_count": 0,
                "disabled_count": 0,
                "type_breakdown": {},
                "scope_breakdown": {},
                "priority_distribution": {}
            }
            
            for rule in all_rules:
                # 启用状态统计
                if rule.is_enabled:
                    summary["enabled_count"] += 1
                else:
                    summary["disabled_count"] += 1
                
                # 规则类型统计
                rule_type = rule.rule_type
                if rule_type not in summary["type_breakdown"]:
                    summary["type_breakdown"][rule_type] = 0
                summary["type_breakdown"][rule_type] += 1
                
                # 应用范围统计
                scope = rule.apply_scope
                if scope not in summary["scope_breakdown"]:
                    summary["scope_breakdown"][scope] = 0
                summary["scope_breakdown"][scope] += 1
                
                # 优先级分布
                priority_range = f"{(rule.priority // 100) * 100}-{(rule.priority // 100) * 100 + 99}"
                if priority_range not in summary["priority_distribution"]:
                    summary["priority_distribution"][priority_range] = 0
                summary["priority_distribution"][priority_range] += 1
            
            return summary
            
        except Exception as e:
            logger.error(f"获取规则统计摘要失败: {e}")
            return {}


# 导入RuleBehaviorMappingRepository避免循环导入
from .rule_behavior_mapping_repository import RuleBehaviorMappingRepository 