2025-07-02 00:06:59 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-02 00:06:59 - __main__ - ERROR - 启动失败: cannot import name 'async_engine' from 'app.database.engine' (D:\MyProject\项目\智能视频监控预警系统\video-ai-analysis\app\database\engine.py)
2025-07-02 00:11:25 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-02 00:11:25 - __main__ - ERROR - 启动失败: cannot import name 'get_db_session' from 'app.database' (D:\MyProject\项目\智能视频监控预警系统\video-ai-analysis\app\database\__init__.py)
2025-07-02 00:12:18 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-02 00:12:18 - __main__ - ERROR - 启动失败: cannot import name 'AIA<PERSON>yzer' from 'app.services.ai' (unknown location)
2025-07-02 00:13:18 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-02 00:13:18 - __main__ - ERROR - 启动失败: cannot import name 'logger' from 'app.core.logger' (D:\MyProject\项目\智能视频监控预警系统\video-ai-analysis\app\core\logger.py)
2025-07-02 00:14:21 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-02 00:14:21 - __main__ - ERROR - 启动失败: cannot import name 'DatabaseError' from 'app.core.exceptions' (D:\MyProject\项目\智能视频监控预警系统\video-ai-analysis\app\core\exceptions.py)
2025-07-02 00:15:10 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-02 00:15:10 - __main__ - ERROR - 启动失败: cannot import name 'AlertService' from 'app.services.alert' (unknown location)
2025-07-02 00:16:08 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-02 00:16:09 - __main__ - ERROR - 启动失败: cannot import name 'AlertServiceException' from 'app.core.exceptions' (D:\MyProject\项目\智能视频监控预警系统\video-ai-analysis\app\core\exceptions.py)
2025-07-02 00:16:44 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-02 00:16:44 - __main__ - ERROR - 启动失败: cannot import name 'FileStorageException' from 'app.core.exceptions' (D:\MyProject\项目\智能视频监控预警系统\video-ai-analysis\app\core\exceptions.py)
2025-07-02 00:17:23 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-02 00:17:28 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000024C6B12CD90>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /video-ai-analysis?location=
2025-07-02 00:17:32 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000024C6B12D120>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /video-ai-analysis?location=
2025-07-02 00:17:37 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000024C6B12D690>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /video-ai-analysis?location=
2025-07-02 00:17:43 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000024C6B12D870>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /video-ai-analysis?location=
2025-07-02 00:17:47 - __main__ - INFO - 👋 收到停止信号，系统已关闭
2025-07-02 00:21:41 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-02 00:21:47 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000219C26DCDC0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /video-ai-analysis?location=
2025-07-02 00:21:51 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000219C26DD150>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /video-ai-analysis?location=
2025-07-02 00:21:56 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000219C26DD6C0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /video-ai-analysis?location=
2025-07-02 00:22:02 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000219C26DD8A0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /video-ai-analysis?location=
2025-07-02 00:22:09 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000219C26DDA80>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /video-ai-analysis?location=
2025-07-02 00:22:13 - app.services.storage.file_storage_service - ERROR - MinIO客户端初始化失败: HTTPConnectionPool(host='localhost', port=9000): Max retries exceeded with url: /video-ai-analysis?location= (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000219C26DDC60>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-02 00:22:13 - __main__ - ERROR - 启动失败: [6000] 存储服务初始化失败: HTTPConnectionPool(host='localhost', port=9000): Max retries exceeded with url: /video-ai-analysis?location= (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000219C26DDC60>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-02 00:22:58 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-02 00:23:03 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001CDEEB8CD90>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /video-ai-analysis?location=
2025-07-02 00:23:07 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001CDEEB8D120>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /video-ai-analysis?location=
2025-07-02 00:23:12 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001CDEEB8D690>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /video-ai-analysis?location=
2025-07-02 00:23:18 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001CDEEB8D870>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /video-ai-analysis?location=
2025-07-02 00:23:25 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001CDEEB8DA50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /video-ai-analysis?location=
2025-07-02 00:23:29 - app.services.storage.file_storage_service - ERROR - MinIO客户端初始化失败: HTTPConnectionPool(host='localhost', port=9000): Max retries exceeded with url: /video-ai-analysis?location= (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001CDEEB8DC30>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-02 00:23:29 - __main__ - ERROR - 启动失败: [6000] 存储服务初始化失败: HTTPConnectionPool(host='localhost', port=9000): Max retries exceeded with url: /video-ai-analysis?location= (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001CDEEB8DC30>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-02 00:27:20 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-02 00:27:24 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001B44490CD90>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /video-ai-analysis?location=
2025-07-02 00:27:29 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001B44490D120>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /video-ai-analysis?location=
2025-07-02 00:27:34 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001B44490D690>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /video-ai-analysis?location=
2025-07-02 00:27:39 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001B44490D870>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /video-ai-analysis?location=
2025-07-02 00:27:46 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001B44490DA50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /video-ai-analysis?location=
2025-07-02 00:27:51 - app.services.storage.file_storage_service - ERROR - MinIO客户端初始化失败: HTTPConnectionPool(host='localhost', port=9000): Max retries exceeded with url: /video-ai-analysis?location= (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001B44490DC30>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-02 00:27:51 - __main__ - ERROR - 启动失败: [6000] 存储服务初始化失败: HTTPConnectionPool(host='localhost', port=9000): Max retries exceeded with url: /video-ai-analysis?location= (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001B44490DC30>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-02 00:28:32 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-02 00:28:33 - app.services.storage.file_storage_service - ERROR - MinIO客户端初始化失败: path in endpoint is not allowed
2025-07-02 00:28:33 - __main__ - ERROR - 启动失败: [6000] 存储服务初始化失败: path in endpoint is not allowed
2025-07-02 00:30:12 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-02 00:30:12 - app.services.storage.file_storage_service - ERROR - MinIO客户端初始化失败: path in endpoint is not allowed
2025-07-02 00:30:12 - __main__ - ERROR - 启动失败: [6000] 存储服务初始化失败: path in endpoint is not allowed
2025-07-02 00:33:18 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-02 00:33:18 - app.services.storage.file_storage_service - INFO - ✅ MinIO客户端初始化成功
2025-07-02 00:33:18 - __main__ - ERROR - 启动失败: cannot import name 'WebSocketException' from 'app.core.exceptions' (D:\MyProject\项目\智能视频监控预警系统\video-ai-analysis\app\core\exceptions.py)
2025-07-02 00:34:05 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-02 00:34:06 - app.services.storage.file_storage_service - INFO - ✅ MinIO客户端初始化成功
2025-07-02 00:34:06 - __main__ - ERROR - 启动失败: no running event loop
2025-07-02 00:35:05 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-02 00:35:06 - app.services.storage.file_storage_service - INFO - ✅ MinIO客户端初始化成功
2025-07-02 00:35:06 - __main__ - ERROR - 启动失败: no running event loop
2025-07-02 00:37:21 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-02 00:37:22 - app.services.storage.file_storage_service - INFO - ✅ MinIO客户端初始化成功
2025-07-02 00:37:22 - __main__ - ERROR - 启动失败: No module named 'app.services.camera.camera_connector'
2025-07-02 00:45:20 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-02 00:45:20 - app.services.storage.file_storage_service - INFO - ✅ MinIO客户端初始化成功
2025-07-02 00:45:20 - __main__ - ERROR - 启动失败: cannot import name 'ConfigurationError' from 'app.core.exceptions' (D:\MyProject\项目\智能视频监控预警系统\video-ai-analysis\app\core\exceptions.py)
2025-07-02 00:48:04 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-02 00:48:04 - app.services.storage.file_storage_service - INFO - ✅ MinIO客户端初始化成功
2025-07-02 00:48:05 - __main__ - ERROR - 启动失败: No module named 'app.core.cache'
2025-07-02 00:52:12 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-02 00:52:13 - app.services.storage.file_storage_service - INFO - ✅ MinIO客户端初始化成功
2025-07-02 00:52:13 - __main__ - ERROR - 启动失败: cannot import name 'SchedulerError' from 'app.core.exceptions' (D:\MyProject\项目\智能视频监控预警系统\video-ai-analysis\app\core\exceptions.py)
2025-07-02 00:53:48 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-02 00:53:49 - app.services.storage.file_storage_service - INFO - ✅ MinIO客户端初始化成功
2025-07-02 00:53:49 - __main__ - ERROR - 启动失败: No module named 'app.services.analysis.ai_analyzer'
