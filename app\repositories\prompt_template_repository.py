"""
提示词模板数据访问层
支持配置驱动的提示词模板管理和优先级选择
算法行为专用模板 > 行为默认模板 > 全局默认模板
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc

from ..database.base_repository import BaseRepository
from ..models.prompt_template import PromptTemplate
from ..core.logger import logger


class PromptTemplateRepository(BaseRepository[PromptTemplate]):
    """提示词模板Repository - AI分析配置驱动核心"""
    
    def __init__(self, db: Session):
        super().__init__(PromptTemplate, db)
    
    async def get_template_by_code(self, code: str) -> Optional[PromptTemplate]:
        """根据模板代码获取模板"""
        return await self.find_by_field("code", code)
    
    async def get_behavior_template(self, behavior_id: int, scenario: str = None) -> Optional[PromptTemplate]:
        """
        获取异常行为的专用模板
        优先级：算法行为专用模板 > 行为默认模板 > 全局默认模板
        """
        try:
            # 1. 首先查找算法行为专用模板（behavior_id + scenario）
            if scenario:
                behavior_scenario_template = self.db.query(PromptTemplate).filter(
                    and_(
                        PromptTemplate.behavior_id == behavior_id,
                        PromptTemplate.scenario == scenario,
                        PromptTemplate.is_enabled == True,
                        PromptTemplate.deleted == False
                    )
                ).first()
                
                if behavior_scenario_template:
                    logger.debug(f"找到行为 {behavior_id} 场景 {scenario} 专用模板: {behavior_scenario_template.id}")
                    return behavior_scenario_template
            
            # 2. 查找行为默认模板（仅behavior_id）
            behavior_template = self.db.query(PromptTemplate).filter(
                and_(
                    PromptTemplate.behavior_id == behavior_id,
                    PromptTemplate.scenario.is_(None),
                    PromptTemplate.is_enabled == True,
                    PromptTemplate.deleted == False
                )
            ).first()
            
            if behavior_template:
                logger.debug(f"找到行为 {behavior_id} 默认模板: {behavior_template.id}")
                return behavior_template
            
            # 3. 查找全局默认模板
            global_template = self.db.query(PromptTemplate).filter(
                and_(
                    PromptTemplate.behavior_id.is_(None),
                    PromptTemplate.is_default == True,
                    PromptTemplate.is_enabled == True,
                    PromptTemplate.deleted == False
                )
            ).first()
            
            if global_template:
                logger.debug(f"使用全局默认模板: {global_template.id}")
                return global_template
            
            logger.warning(f"未找到行为 {behavior_id} 的任何可用模板")
            return None
            
        except Exception as e:
            logger.error(f"获取行为 {behavior_id} 模板失败: {e}")
            return None
    
    async def get_multi_behavior_template(self, behavior_ids: List[int], scenario: str = None) -> Optional[PromptTemplate]:
        """
        获取多行为综合检测模板
        用于同时检测多种异常行为的场景
        """
        try:
            # 查找支持多行为检测的模板
            multi_behavior_template = self.db.query(PromptTemplate).filter(
                and_(
                    PromptTemplate.category == "multi_behavior",
                    PromptTemplate.scenario == scenario if scenario else PromptTemplate.scenario.is_(None),
                    PromptTemplate.is_enabled == True,
                    PromptTemplate.deleted == False
                )
            ).first()
            
            if multi_behavior_template:
                logger.debug(f"找到多行为检测模板: {multi_behavior_template.id}")
                return multi_behavior_template
            
            # 如果没有专用的多行为模板，使用通用检测模板
            general_template = self.db.query(PromptTemplate).filter(
                and_(
                    PromptTemplate.category == "general_detection",
                    PromptTemplate.is_enabled == True,
                    PromptTemplate.deleted == False
                )
            ).first()
            
            if general_template:
                logger.debug(f"使用通用检测模板: {general_template.id}")
                return general_template
            
            return None
            
        except Exception as e:
            logger.error(f"获取多行为模板失败: {e}")
            return None
    
    async def build_detection_prompt(self, behavior_configs: List[Dict[str, Any]], 
                                   scenario: str = None) -> str:
        """
        构建AI检测提示词
        根据行为配置动态构建综合检测提示词
        """
        try:
            if not behavior_configs:
                return ""
            
            if len(behavior_configs) == 1:
                # 单行为检测
                behavior = behavior_configs[0]
                template = await self.get_behavior_template(behavior["behavior_id"], scenario)
                if template:
                    return self._render_template(template.template_content, behavior)
            else:
                # 多行为检测
                behavior_ids = [b["behavior_id"] for b in behavior_configs]
                template = await self.get_multi_behavior_template(behavior_ids, scenario)
                if template:
                    return self._render_multi_behavior_template(template.template_content, behavior_configs)
            
            # 如果没有找到模板，构建基础提示词
            return self._build_basic_prompt(behavior_configs)
            
        except Exception as e:
            logger.error(f"构建检测提示词失败: {e}")
            return self._build_basic_prompt(behavior_configs)
    
    def _render_template(self, template_content: str, behavior_config: Dict[str, Any]) -> str:
        """渲染单行为模板"""
        try:
            # 支持的模板变量
            variables = {
                "behavior_name": behavior_config.get("behavior_name", ""),
                "behavior_description": behavior_config.get("description", ""),
                "ai_keywords": ", ".join(behavior_config.get("ai_keywords", [])),
                "confidence_threshold": behavior_config.get("confidence_threshold", 0.7),
                "severity": behavior_config.get("severity", "medium")
            }
            
            # 简单的模板变量替换
            rendered = template_content
            for key, value in variables.items():
                rendered = rendered.replace(f"{{{key}}}", str(value))
            
            return rendered
            
        except Exception as e:
            logger.error(f"渲染模板失败: {e}")
            return template_content
    
    def _render_multi_behavior_template(self, template_content: str, 
                                      behavior_configs: List[Dict[str, Any]]) -> str:
        """渲染多行为模板"""
        try:
            # 构建行为列表
            behavior_list = []
            keyword_list = []
            
            for behavior in behavior_configs:
                behavior_list.append(f"- {behavior.get('behavior_name', '')}: {behavior.get('description', '')}")
                keyword_list.extend(behavior.get('ai_keywords', []))
            
            variables = {
                "behavior_list": "\n".join(behavior_list),
                "all_keywords": ", ".join(set(keyword_list)),
                "behavior_count": len(behavior_configs)
            }
            
            # 模板变量替换
            rendered = template_content
            for key, value in variables.items():
                rendered = rendered.replace(f"{{{key}}}", str(value))
            
            return rendered
            
        except Exception as e:
            logger.error(f"渲染多行为模板失败: {e}")
            return template_content
    
    def _build_basic_prompt(self, behavior_configs: List[Dict[str, Any]]) -> str:
        """构建基础提示词（当没有模板时使用）"""
        behavior_descriptions = []
        all_keywords = set()
        
        for behavior in behavior_configs:
            name = behavior.get("behavior_name", "")
            desc = behavior.get("description", "")
            keywords = behavior.get("ai_keywords", [])
            
            behavior_descriptions.append(f"- {name}: {desc}")
            all_keywords.update(keywords)
        
        behaviors_text = "\n".join(behavior_descriptions)
        keywords_text = ", ".join(all_keywords)
        
        return f"""请分析这张图片中是否存在以下异常行为：

{behaviors_text}

关键识别要素：{keywords_text}

请仔细观察图片中的人员行为、动作、姿态和环境情况，判断是否存在上述任何一种异常行为。

如果发现异常，请说明：
1. 具体的异常行为类型
2. 置信度（0-1之间的数值）
3. 详细的观察描述

如果没有发现异常，请简要说明图片中的正常情况。"""
    
    async def get_templates_by_category(self, category: str) -> List[PromptTemplate]:
        """根据分类获取模板列表"""
        return await self.find_by_field("category", category)
    
    async def get_performance_templates(self, limit: int = 10) -> List[PromptTemplate]:
        """获取性能最佳的模板"""
        try:
            templates = self.db.query(PromptTemplate).filter(
                and_(
                    PromptTemplate.is_enabled == True,
                    PromptTemplate.deleted == False,
                    PromptTemplate.performance_score.is_not(None)
                )
            ).order_by(desc(PromptTemplate.performance_score)).limit(limit).all()
            
            return templates
            
        except Exception as e:
            logger.error(f"获取性能最佳模板失败: {e}")
            return []
    
    async def update_template_performance(self, template_id: int, 
                                        success_count: int = None,
                                        total_count: int = None,
                                        avg_response_time: int = None) -> bool:
        """更新模板性能统计"""
        try:
            update_data = {}
            
            if success_count is not None and total_count is not None:
                success_rate = success_count / max(total_count, 1)
                update_data["success_rate"] = success_rate
                update_data["usage_count"] = total_count
            
            if avg_response_time is not None:
                update_data["avg_response_time_ms"] = avg_response_time
            
            if update_data:
                success = await self.update_by_id(template_id, update_data)
                if success:
                    logger.debug(f"更新模板 {template_id} 性能统计")
                return success
            
            return True
            
        except Exception as e:
            logger.error(f"更新模板 {template_id} 性能统计失败: {e}")
            return False 