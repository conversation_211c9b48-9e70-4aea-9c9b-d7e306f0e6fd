#!/usr/bin/env python3
"""
最小化测试 - 隔离CORS配置问题
"""
import os
from typing import List
from pydantic import BaseModel, Field, field_validator, ConfigDict
from pydantic_settings import BaseSettings

# 设置环境变量
os.environ["CORS_ORIGINS"] = "*"

class MinimalServerSettings(BaseSettings):
    """最小化服务器配置"""
    cors_origins: List[str] = Field(default=["*"], env="CORS_ORIGINS")
    
    model_config = ConfigDict(extra="ignore")
    
    @field_validator("cors_origins", mode="before")
    @classmethod
    def parse_cors_origins(cls, v):
        print(f"🔍 Validator接收到: {v!r}, 类型: {type(v)}")
        if isinstance(v, str):
            if v == "*":
                result = ["*"]
            else:
                result = [origin.strip() for origin in v.split(",") if origin.strip()]
            print(f"✅ 解析结果: {result}")
            return result
        print(f"📋 直接返回: {v}")
        return v

def test_minimal():
    """测试最小化配置"""
    print("🚀 开始最小化测试...")
    
    try:
        print(f"环境变量 CORS_ORIGINS: {os.getenv('CORS_ORIGINS')!r}")
        
        settings = MinimalServerSettings()
        print(f"✅ 配置创建成功!")
        print(f"CORS Origins: {settings.cors_origins}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_minimal()
