"""
Redis缓存服务
提供统一的缓存接口和管理
"""

import json
import logging
from typing import Any, Optional, Union, Dict, List
from datetime import datetime, timedelta
import redis.asyncio as redis
from redis.asyncio import Redis
from .config import settings

logger = logging.getLogger(__name__)


class RedisCache:
    """Redis缓存服务"""
    
    def __init__(self):
        self._redis: Optional[Redis] = None
        self._connected = False
    
    async def connect(self) -> bool:
        """连接到Redis"""
        try:
            self._redis = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                password=settings.REDIS_PASSWORD if settings.REDIS_PASSWORD else None,
                db=settings.REDIS_DB,
                decode_responses=True,
                socket_timeout=settings.REDIS_TIMEOUT,
                socket_connect_timeout=settings.REDIS_TIMEOUT,
                retry_on_timeout=True,
                max_connections=settings.REDIS_POOL_SIZE
            )
            
            # 测试连接
            await self._redis.ping()
            self._connected = True
            logger.info("✅ Redis缓存连接成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ Redis缓存连接失败: {e}")
            self._connected = False
            return False
    
    async def disconnect(self):
        """断开Redis连接"""
        if self._redis:
            await self._redis.close()
            self._connected = False
            logger.info("Redis缓存连接已关闭")
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self._connected
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        expire: Optional[Union[int, timedelta]] = None
    ) -> bool:
        """设置缓存值"""
        if not self._connected or not self._redis:
            logger.warning("Redis未连接，无法设置缓存")
            return False
        
        try:
            # 序列化值
            if isinstance(value, (dict, list)):
                value = json.dumps(value, ensure_ascii=False)
            elif not isinstance(value, str):
                value = str(value)
            
            # 设置过期时间
            if isinstance(expire, timedelta):
                expire = int(expire.total_seconds())
            
            await self._redis.set(key, value, ex=expire)
            return True
            
        except Exception as e:
            logger.error(f"设置缓存失败 {key}: {e}")
            return False
    
    async def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值"""
        if not self._connected or not self._redis:
            logger.warning("Redis未连接，无法获取缓存")
            return default
        
        try:
            value = await self._redis.get(key)
            if value is None:
                return default
            
            # 尝试反序列化JSON
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value
                
        except Exception as e:
            logger.error(f"获取缓存失败 {key}: {e}")
            return default
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        if not self._connected or not self._redis:
            logger.warning("Redis未连接，无法删除缓存")
            return False
        
        try:
            result = await self._redis.delete(key)
            return result > 0
            
        except Exception as e:
            logger.error(f"删除缓存失败 {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        if not self._connected or not self._redis:
            return False
        
        try:
            result = await self._redis.exists(key)
            return result > 0
            
        except Exception as e:
            logger.error(f"检查缓存存在性失败 {key}: {e}")
            return False
    
    async def expire(self, key: str, seconds: int) -> bool:
        """设置缓存过期时间"""
        if not self._connected or not self._redis:
            return False
        
        try:
            result = await self._redis.expire(key, seconds)
            return result
            
        except Exception as e:
            logger.error(f"设置缓存过期时间失败 {key}: {e}")
            return False
    
    async def ttl(self, key: str) -> int:
        """获取缓存剩余生存时间"""
        if not self._connected or not self._redis:
            return -1
        
        try:
            return await self._redis.ttl(key)
            
        except Exception as e:
            logger.error(f"获取缓存TTL失败 {key}: {e}")
            return -1
    
    async def keys(self, pattern: str = "*") -> List[str]:
        """获取匹配模式的所有键"""
        if not self._connected or not self._redis:
            return []
        
        try:
            return await self._redis.keys(pattern)
            
        except Exception as e:
            logger.error(f"获取缓存键列表失败 {pattern}: {e}")
            return []
    
    async def clear_pattern(self, pattern: str) -> int:
        """清除匹配模式的所有缓存"""
        if not self._connected or not self._redis:
            return 0
        
        try:
            keys = await self.keys(pattern)
            if keys:
                return await self._redis.delete(*keys)
            return 0
            
        except Exception as e:
            logger.error(f"清除缓存模式失败 {pattern}: {e}")
            return 0
    
    async def flush_all(self) -> bool:
        """清空所有缓存"""
        if not self._connected or not self._redis:
            return False
        
        try:
            await self._redis.flushdb()
            logger.info("已清空所有缓存")
            return True
            
        except Exception as e:
            logger.error(f"清空缓存失败: {e}")
            return False
    
    async def ping(self) -> bool:
        """测试连接"""
        if not self._redis:
            return False
        
        try:
            await self._redis.ping()
            return True
            
        except Exception as e:
            logger.error(f"Redis ping失败: {e}")
            self._connected = False
            return False
    
    async def info(self) -> Dict[str, Any]:
        """获取Redis信息"""
        if not self._connected or not self._redis:
            return {}
        
        try:
            info = await self._redis.info()
            return info
            
        except Exception as e:
            logger.error(f"获取Redis信息失败: {e}")
            return {}


# 全局缓存实例
cache = RedisCache()


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, cache_instance: RedisCache = None):
        self.cache = cache_instance or cache
    
    async def get_or_set(
        self, 
        key: str, 
        func, 
        expire: Optional[Union[int, timedelta]] = None,
        *args, 
        **kwargs
    ) -> Any:
        """获取缓存，如果不存在则执行函数并设置缓存"""
        # 先尝试获取缓存
        result = await self.cache.get(key)
        if result is not None:
            return result
        
        # 执行函数获取结果
        if callable(func):
            if hasattr(func, '__call__') and hasattr(func, '__await__'):
                # 异步函数
                result = await func(*args, **kwargs)
            else:
                # 同步函数
                result = func(*args, **kwargs)
        else:
            result = func
        
        # 设置缓存
        await self.cache.set(key, result, expire)
        return result
    
    def cache_key(self, prefix: str, *parts) -> str:
        """生成缓存键"""
        key_parts = [prefix] + [str(part) for part in parts]
        return ":".join(key_parts)


# 全局缓存管理器
cache_manager = CacheManager() 