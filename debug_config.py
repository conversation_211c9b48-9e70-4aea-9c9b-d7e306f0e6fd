#!/usr/bin/env python3
"""
调试配置解析问题
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def debug_cors_parsing():
    """调试CORS配置解析"""
    print("🔍 调试CORS配置解析...")
    
    # 检查环境变量
    cors_origins_env = os.getenv("CORS_ORIGINS")
    print(f"环境变量 CORS_ORIGINS: {cors_origins_env!r}")
    print(f"类型: {type(cors_origins_env)}")
    
    # 测试解析逻辑
    def parse_cors_origins(v):
        print(f"输入值: {v!r}, 类型: {type(v)}")
        if isinstance(v, str):
            if v == "*":
                result = ["*"]
            else:
                result = [origin.strip() for origin in v.split(",") if origin.strip()]
            print(f"解析结果: {result}")
            return result
        print(f"直接返回: {v}")
        return v
    
    # 测试解析
    test_result = parse_cors_origins(cors_origins_env)
    print(f"最终结果: {test_result}")

def test_pydantic_field():
    """测试Pydantic字段解析"""
    print("\n🔍 测试Pydantic字段解析...")
    
    try:
        from pydantic import BaseModel, Field, field_validator
        from typing import List
        
        class TestConfig(BaseModel):
            cors_origins: List[str] = Field(default=["*"], env="CORS_ORIGINS")
            
            @field_validator("cors_origins", mode="before")
            @classmethod
            def parse_cors_origins(cls, v):
                print(f"Validator接收到: {v!r}, 类型: {type(v)}")
                if isinstance(v, str):
                    if v == "*":
                        return ["*"]
                    return [origin.strip() for origin in v.split(",") if origin.strip()]
                return v
        
        # 测试创建配置
        config = TestConfig()
        print(f"✅ 配置创建成功: {config.cors_origins}")
        
    except Exception as e:
        print(f"❌ Pydantic测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_server_settings():
    """测试ServerSettings"""
    print("\n🔍 测试ServerSettings...")
    
    try:
        from app.core.config import ServerSettings
        
        server_settings = ServerSettings()
        print(f"✅ ServerSettings创建成功")
        print(f"CORS Origins: {server_settings.cors_origins}")
        
    except Exception as e:
        print(f"❌ ServerSettings测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 开始调试配置解析问题...")
    print("=" * 50)
    
    debug_cors_parsing()
    test_pydantic_field()
    test_server_settings()
    
    print("=" * 50)
    print("🏁 调试完成")

if __name__ == "__main__":
    main()
