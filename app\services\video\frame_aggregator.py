"""
帧聚合器 - 三层优化架构的第二层

主要功能：
1. 聚合相似的视频帧
2. 时间窗口内的帧合并
3. 智能采样和关键帧提取
4. 减少冗余AI分析调用
5. 批量处理优化
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import cv2

from .frame_buffer import VideoFrame
from .pre_filter import PreFilterResult, FilterDecision

logger = logging.getLogger(__name__)


class AggregationStrategy(Enum):
    """聚合策略"""
    TIME_WINDOW = "time_window"  # 时间窗口聚合
    SIMILARITY_BASED = "similarity_based"  # 相似度聚合
    MOTION_BASED = "motion_based"  # 运动变化聚合
    HYBRID = "hybrid"  # 混合策略


class AggregationDecision(Enum):
    """聚合决策"""
    AGGREGATE = "aggregate"  # 聚合到现有组
    NEW_GROUP = "new_group"  # 创建新组
    IMMEDIATE_PROCESS = "immediate_process"  # 立即处理
    DISCARD = "discard"  # 丢弃


@dataclass
class FrameGroup:
    """帧组"""
    group_id: str
    source_id: str
    frames: List[VideoFrame] = field(default_factory=list)
    filter_results: List[PreFilterResult] = field(default_factory=list)
    
    # 时间信息
    start_timestamp: float = 0.0
    end_timestamp: float = 0.0
    duration: float = 0.0
    
    # 聚合信息
    representative_frame: Optional[VideoFrame] = None
    representative_filter_result: Optional[PreFilterResult] = None
    aggregation_strategy: AggregationStrategy = AggregationStrategy.TIME_WINDOW
    
    # 统计信息
    avg_motion_score: float = 0.0
    max_motion_score: float = 0.0
    
    created_at: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        if self.frames:
            self.start_timestamp = min(frame.timestamp for frame in self.frames)
            self.end_timestamp = max(frame.timestamp for frame in self.frames)
            self.duration = self.end_timestamp - self.start_timestamp
            
            # 选择代表帧（选择运动分数最高的帧）
            max_motion_frame = None
            max_motion_score = 0.0
            
            for i, frame in enumerate(self.frames):
                if i < len(self.filter_results):
                    filter_result = self.filter_results[i]
                    if (filter_result.motion_result and 
                        filter_result.motion_result.motion_score > max_motion_score):
                        max_motion_score = filter_result.motion_result.motion_score
                        max_motion_frame = frame
                        self.representative_filter_result = filter_result
            
            if max_motion_frame:
                self.representative_frame = max_motion_frame
            else:
                # 如果没有运动信息，选择中间帧
                mid_index = len(self.frames) // 2
                self.representative_frame = self.frames[mid_index]
                if mid_index < len(self.filter_results):
                    self.representative_filter_result = self.filter_results[mid_index]
    
    def add_frame(self, frame: VideoFrame, filter_result: PreFilterResult):
        """添加帧到组"""
        self.frames.append(frame)
        self.filter_results.append(filter_result)
        
        # 更新时间信息
        if not self.start_timestamp or frame.timestamp < self.start_timestamp:
            self.start_timestamp = frame.timestamp
        if frame.timestamp > self.end_timestamp:
            self.end_timestamp = frame.timestamp
        self.duration = self.end_timestamp - self.start_timestamp
        
        # 更新代表帧
        if (filter_result.motion_result and 
            filter_result.motion_result.motion_score > self.max_motion_score):
            self.representative_frame = frame
            self.representative_filter_result = filter_result
            self.max_motion_score = filter_result.motion_result.motion_score
    
    def should_close(self, max_duration: float = 5.0, max_frames: int = 10) -> bool:
        """判断是否应该关闭组"""
        current_time = time.time()
        
        # 时间限制
        if self.duration >= max_duration:
            return True
        
        # 帧数限制
        if len(self.frames) >= max_frames:
            return True
        
        # 空闲时间限制
        if current_time - self.created_at > max_duration * 2:
            return True
        
        return False
    
    def get_frame_count(self) -> int:
        """获取帧数"""
        return len(self.frames)
    
    def get_compression_ratio(self) -> float:
        """获取压缩比"""
        if len(self.frames) <= 1:
            return 1.0
        return 1.0 / len(self.frames)


@dataclass
class AggregationStatistics:
    """聚合统计"""
    total_frames_received: int = 0
    total_groups_created: int = 0
    total_groups_processed: int = 0
    
    frames_aggregated: int = 0
    frames_immediate_processed: int = 0
    frames_discarded: int = 0
    
    avg_group_size: float = 0.0
    avg_compression_ratio: float = 0.0
    
    api_calls_saved: int = 0
    cost_savings_percentage: float = 0.0


class FrameAggregator:
    """帧聚合器"""
    
    def __init__(
        self,
        time_window_seconds: float = 3.0,
        similarity_threshold: float = 0.8,
        max_group_size: int = 10,
        max_groups_per_source: int = 5,
        aggregation_strategy: AggregationStrategy = AggregationStrategy.TIME_WINDOW
    ):
        self.time_window_seconds = time_window_seconds
        self.similarity_threshold = similarity_threshold
        self.max_group_size = max_group_size
        self.max_groups_per_source = max_groups_per_source
        self.aggregation_strategy = aggregation_strategy
        
        # 活跃的帧组
        self._active_groups: Dict[str, List[FrameGroup]] = defaultdict(list)
        
        # 完成的帧组队列
        self._completed_groups: deque = deque(maxlen=1000)
        
        # 统计信息
        self._statistics: Dict[str, AggregationStatistics] = {}
        self._global_statistics = AggregationStatistics()
        
        logger.info("帧聚合器已初始化")
    
    def aggregate_frame(
        self, 
        frame: VideoFrame, 
        filter_result: PreFilterResult
    ) -> Tuple[AggregationDecision, Optional[FrameGroup]]:
        """聚合帧"""
        start_time = time.time()
        source_id = frame.source_id
        
        try:
            # 更新统计
            self._update_statistics(source_id, received_frame=True)
            
            # 立即处理的情况
            if self._should_process_immediately(filter_result):
                # 创建单帧组
                group = FrameGroup(
                    group_id=f"{source_id}_{frame.frame_id}",
                    source_id=source_id,
                    frames=[frame],
                    filter_results=[filter_result],
                    aggregation_strategy=AggregationStrategy.TIME_WINDOW
                )
                
                self._update_statistics(source_id, immediate_processed=True)
                return AggregationDecision.IMMEDIATE_PROCESS, group
            
            # 检查是否应该丢弃
            if self._should_discard_frame(filter_result):
                self._update_statistics(source_id, discarded=True)
                return AggregationDecision.DISCARD, None
            
            # 寻找合适的组
            target_group = self._find_suitable_group(source_id, frame, filter_result)
            
            if target_group:
                # 添加到现有组
                target_group.add_frame(frame, filter_result)
                self._update_statistics(source_id, aggregated=True)
                
                # 检查组是否应该完成
                if target_group.should_close(self.time_window_seconds, self.max_group_size):
                    self._complete_group(source_id, target_group)
                    return AggregationDecision.NEW_GROUP, target_group
                
                return AggregationDecision.AGGREGATE, None
            else:
                # 创建新组
                new_group = self._create_new_group(source_id, frame, filter_result)
                return AggregationDecision.NEW_GROUP, new_group
            
        except Exception as e:
            logger.error(f"帧聚合失败 - 源: {source_id}, 错误: {e}")
            return AggregationDecision.IMMEDIATE_PROCESS, None
    
    def _should_process_immediately(self, filter_result: PreFilterResult) -> bool:
        """判断是否应该立即处理"""
        # 高优先级的帧立即处理
        if filter_result.decision == FilterDecision.HIGH_PRIORITY:
            return True
        
        # 运动变化很大的帧立即处理
        if (filter_result.motion_result and 
            filter_result.motion_result.motion_score > 0.8):
            return True
        
        return False
    
    def _should_discard_frame(self, filter_result: PreFilterResult) -> bool:
        """判断是否应该丢弃帧"""
        # 跳过的帧直接丢弃
        if filter_result.decision == FilterDecision.SKIP:
            return True
        
        # 相似度过高的帧可能丢弃
        if (filter_result.similarity_result and 
            filter_result.similarity_result.max_similarity > 0.95):
            return True
        
        return False
    
    def _find_suitable_group(
        self, 
        source_id: str, 
        frame: VideoFrame, 
        filter_result: PreFilterResult
    ) -> Optional[FrameGroup]:
        """寻找合适的组"""
        if source_id not in self._active_groups:
            return None
        
        current_time = frame.timestamp
        best_group = None
        best_score = 0.0
        
        for group in self._active_groups[source_id]:
            # 检查时间窗口
            if current_time - group.end_timestamp > self.time_window_seconds:
                continue
            
            # 检查组大小限制
            if group.get_frame_count() >= self.max_group_size:
                continue
            
            # 计算匹配分数
            score = self._calculate_group_match_score(group, frame, filter_result)
            
            if score > best_score and score > 0.5:  # 最低匹配阈值
                best_score = score
                best_group = group
        
        return best_group
    
    def _calculate_group_match_score(
        self, 
        group: FrameGroup, 
        frame: VideoFrame, 
        filter_result: PreFilterResult
    ) -> float:
        """计算组匹配分数"""
        if not group.representative_frame:
            return 0.0
        
        score = 0.0
        
        # 时间相似度 (50%)
        time_diff = abs(frame.timestamp - group.end_timestamp)
        time_score = max(0, 1.0 - (time_diff / self.time_window_seconds))
        score += time_score * 0.5
        
        # 运动相似度 (50%)
        if (filter_result.motion_result and 
            group.representative_filter_result and 
            group.representative_filter_result.motion_result):
            
            motion_diff = abs(
                filter_result.motion_result.motion_score - 
                group.representative_filter_result.motion_result.motion_score
            )
            motion_score = max(0, 1.0 - motion_diff)
            score += motion_score * 0.5
        
        return score
    
    def _create_new_group(
        self, 
        source_id: str, 
        frame: VideoFrame, 
        filter_result: PreFilterResult
    ) -> FrameGroup:
        """创建新组"""
        # 清理旧组
        self._cleanup_old_groups(source_id)
        
        # 创建新组
        group_id = f"{source_id}_{int(frame.timestamp)}_{frame.frame_id[:8]}"
        new_group = FrameGroup(
            group_id=group_id,
            source_id=source_id,
            frames=[frame],
            filter_results=[filter_result],
            aggregation_strategy=self.aggregation_strategy
        )
        
        # 添加到活跃组
        if source_id not in self._active_groups:
            self._active_groups[source_id] = []
        
        self._active_groups[source_id].append(new_group)
        
        # 更新统计
        self._update_statistics(source_id, new_group=True)
        
        return new_group
    
    def _complete_group(self, source_id: str, group: FrameGroup):
        """完成组"""
        if source_id in self._active_groups:
            if group in self._active_groups[source_id]:
                self._active_groups[source_id].remove(group)
        
        # 添加到完成队列
        self._completed_groups.append(group)
        
        # 更新统计
        self._update_statistics(source_id, processed_group=True, group_size=group.get_frame_count())
    
    def _cleanup_old_groups(self, source_id: str):
        """清理旧组"""
        if source_id not in self._active_groups:
            return
        
        current_time = time.time()
        groups_to_complete = []
        
        for group in self._active_groups[source_id]:
            # 检查是否应该关闭
            if (current_time - group.created_at > self.time_window_seconds * 2 or
                group.should_close(self.time_window_seconds, self.max_group_size)):
                groups_to_complete.append(group)
        
        # 完成旧组
        for group in groups_to_complete:
            self._complete_group(source_id, group)
        
        # 限制每个源的组数量
        if len(self._active_groups[source_id]) > self.max_groups_per_source:
            # 完成最旧的组
            oldest_groups = sorted(
                self._active_groups[source_id], 
                key=lambda g: g.created_at
            )[:len(self._active_groups[source_id]) - self.max_groups_per_source]
            
            for group in oldest_groups:
                self._complete_group(source_id, group)
    
    def get_completed_groups(self, max_count: int = 10) -> List[FrameGroup]:
        """获取完成的组"""
        groups = []
        for _ in range(min(max_count, len(self._completed_groups))):
            if self._completed_groups:
                groups.append(self._completed_groups.popleft())
        return groups
    
    def get_active_groups_count(self, source_id: Optional[str] = None) -> int:
        """获取活跃组数量"""
        if source_id:
            return len(self._active_groups.get(source_id, []))
        else:
            return sum(len(groups) for groups in self._active_groups.values())
    
    def force_complete_all_groups(self, source_id: Optional[str] = None):
        """强制完成所有组"""
        if source_id:
            if source_id in self._active_groups:
                groups_to_complete = list(self._active_groups[source_id])
                for group in groups_to_complete:
                    self._complete_group(source_id, group)
        else:
            for sid in list(self._active_groups.keys()):
                groups_to_complete = list(self._active_groups[sid])
                for group in groups_to_complete:
                    self._complete_group(sid, group)
    
    def _update_statistics(
        self, 
        source_id: str, 
        received_frame: bool = False,
        aggregated: bool = False,
        immediate_processed: bool = False,
        discarded: bool = False,
        new_group: bool = False,
        processed_group: bool = False,
        group_size: int = 0
    ):
        """更新统计信息"""
        if source_id not in self._statistics:
            self._statistics[source_id] = AggregationStatistics()
        
        stats = self._statistics[source_id]
        global_stats = self._global_statistics
        
        if received_frame:
            stats.total_frames_received += 1
            global_stats.total_frames_received += 1
        
        if aggregated:
            stats.frames_aggregated += 1
            global_stats.frames_aggregated += 1
        
        if immediate_processed:
            stats.frames_immediate_processed += 1
            global_stats.frames_immediate_processed += 1
        
        if discarded:
            stats.frames_discarded += 1
            global_stats.frames_discarded += 1
        
        if new_group:
            stats.total_groups_created += 1
            global_stats.total_groups_created += 1
        
        if processed_group:
            stats.total_groups_processed += 1
            global_stats.total_groups_processed += 1
            
            # 更新平均组大小
            if stats.total_groups_processed > 0:
                total_frames = stats.frames_aggregated + stats.frames_immediate_processed
                stats.avg_group_size = total_frames / stats.total_groups_processed
                
                # 计算压缩比和成本节省
                if stats.total_frames_received > 0:
                    stats.avg_compression_ratio = stats.total_groups_processed / stats.total_frames_received
                    
                    # 计算节省的API调用
                    api_calls_saved = stats.total_frames_received - stats.total_groups_processed
                    stats.api_calls_saved = api_calls_saved
                    stats.cost_savings_percentage = (api_calls_saved / stats.total_frames_received) * 100
            
            # 更新全局统计
            if global_stats.total_groups_processed > 0:
                total_frames = global_stats.frames_aggregated + global_stats.frames_immediate_processed
                global_stats.avg_group_size = total_frames / global_stats.total_groups_processed
                
                if global_stats.total_frames_received > 0:
                    global_stats.avg_compression_ratio = global_stats.total_groups_processed / global_stats.total_frames_received
                    
                    api_calls_saved = global_stats.total_frames_received - global_stats.total_groups_processed
                    global_stats.api_calls_saved = api_calls_saved
                    global_stats.cost_savings_percentage = (api_calls_saved / global_stats.total_frames_received) * 100
    
    def get_statistics(self, source_id: Optional[str] = None) -> Dict[str, AggregationStatistics]:
        """获取统计信息"""
        if source_id:
            return {source_id: self._statistics.get(source_id, AggregationStatistics())}
        else:
            return dict(self._statistics)
    
    def get_global_statistics(self) -> AggregationStatistics:
        """获取全局统计信息"""
        return self._global_statistics
    
    def cleanup_cache(self):
        """清理缓存"""
        # 清理完成的组队列
        if len(self._completed_groups) > 500:
            # 只保留最新的500个
            while len(self._completed_groups) > 500:
                self._completed_groups.popleft()
        
        logger.info("帧聚合器缓存已清理")


@dataclass
class AggregationResult:
    """聚合结果"""
    decision: AggregationDecision
    group: Optional[FrameGroup] = None
    processing_time_ms: float = 0.0
    reason: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict) 