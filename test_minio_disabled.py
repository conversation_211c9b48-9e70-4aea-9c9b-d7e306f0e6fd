#!/usr/bin/env python3
"""
测试MinIO禁用配置
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def main():
    """测试MinIO配置"""
    print("🔧 测试MinIO禁用配置...")
    
    # 加载环境变量
    from dotenv import load_dotenv
    load_dotenv()
    
    # 检查环境变量
    minio_enabled = os.getenv("MINIO_ENABLED")
    print(f"环境变量 MINIO_ENABLED: {minio_enabled!r}")
    
    # 测试配置加载
    try:
        from app.core.config import settings
        print(f"配置中的 MinIO enabled: {settings.minio.enabled}")
        
        # 测试存储服务
        from app.services.storage.file_storage_service import FileStorageService
        storage = FileStorageService()
        
        if storage.use_local_storage:
            print("✅ 成功！使用本地文件存储")
            print(f"存储路径: {storage.local_storage_path}")
        else:
            print("❌ 失败！仍在使用MinIO")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    print("🎉 配置测试完成！")
    return 0

if __name__ == "__main__":
    sys.exit(main())
