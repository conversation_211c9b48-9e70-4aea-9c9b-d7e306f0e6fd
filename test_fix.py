#!/usr/bin/env python3
"""
测试修复效果
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_server_settings():
    """测试ServerSettings"""
    print("🔧 测试ServerSettings...")
    
    try:
        from app.core.config import ServerSettings
        
        print("✅ ServerSettings导入成功")
        
        # 测试创建实例
        server_settings = ServerSettings()
        print(f"✅ ServerSettings创建成功")
        print(f"CORS Origins: {server_settings.cors_origins}")
        print(f"Host: {server_settings.host}")
        print(f"Port: {server_settings.port}")
        
        return True
        
    except Exception as e:
        print(f"❌ ServerSettings测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_settings():
    """测试主Settings类"""
    print("\n🔧 测试主Settings类...")
    
    try:
        from app.core.config import Settings
        
        print("✅ Settings导入成功")
        
        # 测试创建实例
        settings = Settings()
        print(f"✅ Settings创建成功")
        print(f"应用名称: {settings.app_name}")
        print(f"环境: {settings.environment}")
        print(f"CORS Origins: {settings.server.cors_origins}")
        
        return True
        
    except Exception as e:
        print(f"❌ Settings测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_import():
    """测试主模块导入"""
    print("\n🔧 测试主模块导入...")
    
    try:
        import app.main
        print("✅ 主模块导入成功")
        return True
        
    except Exception as e:
        print(f"❌ 主模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 测试修复效果...")
    print("=" * 50)
    
    tests = [
        ("ServerSettings", test_server_settings),
        ("主Settings类", test_main_settings),
        ("主模块导入", test_main_import),
    ]
    
    results = []
    for test_name, test_func in tests:
        result = test_func()
        results.append((test_name, result))
    
    print("=" * 50)
    print("📊 测试结果:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过! 修复成功!")
        print("现在可以尝试启动系统: python start.py")
    else:
        print("\n💥 部分测试失败，需要进一步检查")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
