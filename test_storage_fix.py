#!/usr/bin/env python3
"""
测试存储服务修复
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_storage_service():
    """测试存储服务"""
    print("🔧 测试存储服务...")
    
    try:
        # 加载环境变量
        from dotenv import load_dotenv
        load_dotenv()
        
        from app.services.storage.file_storage_service import FileStorageService
        
        print("✅ 存储服务导入成功")
        
        # 创建存储服务实例
        storage_service = FileStorageService()
        print("✅ 存储服务初始化成功")
        
        if storage_service.use_local_storage:
            print("📁 使用本地文件存储")
            print(f"存储路径: {storage_service.local_storage_path}")
        else:
            print("☁️ 使用MinIO对象存储")
            print(f"存储桶: {storage_service.bucket_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 存储服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_startup():
    """测试主程序启动"""
    print("\n🔧 测试主程序启动...")
    
    try:
        from app.core.config import Settings
        
        settings = Settings()
        print(f"✅ 配置加载成功")
        print(f"MinIO启用状态: {settings.minio.enabled}")
        
        # 测试主模块导入
        import app.main
        print("✅ 主模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 主程序测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 测试存储服务修复...")
    print("=" * 50)
    
    tests = [
        ("存储服务", test_storage_service),
        ("主程序启动", test_main_startup),
    ]
    
    results = []
    for test_name, test_func in tests:
        result = test_func()
        results.append((test_name, result))
    
    print("=" * 50)
    print("📊 测试结果:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 存储服务修复成功!")
        print("现在可以启动系统: python start.py")
        print("\n💡 提示:")
        print("- 当前使用本地文件存储")
        print("- 要启用MinIO，请修复连接问题后设置 MINIO_ENABLED=true")
    else:
        print("\n💥 修复失败，需要进一步检查")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
