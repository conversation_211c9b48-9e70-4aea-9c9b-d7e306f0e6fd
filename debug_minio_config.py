#!/usr/bin/env python3
"""
调试MinIO配置加载
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def debug_env_loading():
    """调试环境变量加载"""
    print("🔍 调试环境变量加载...")
    
    # 手动加载.env文件
    from dotenv import load_dotenv
    load_dotenv()
    
    # 检查环境变量
    minio_enabled = os.getenv("MINIO_ENABLED")
    print(f"环境变量 MINIO_ENABLED: {minio_enabled!r}")
    print(f"类型: {type(minio_enabled)}")
    
    # 检查布尔值转换
    enabled_bool = minio_enabled.lower() == "true" if minio_enabled else True
    print(f"转换为布尔值: {enabled_bool}")

def debug_config_loading():
    """调试配置加载"""
    print("\n🔍 调试配置加载...")
    
    try:
        from app.core.config import settings
        
        print(f"MinIO配置:")
        print(f"  enabled: {settings.minio.enabled} (类型: {type(settings.minio.enabled)})")
        print(f"  endpoint: {settings.minio.endpoint}")
        print(f"  bucket_name: {settings.minio.bucket_name}")
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        import traceback
        traceback.print_exc()

def debug_storage_service():
    """调试存储服务"""
    print("\n🔍 调试存储服务...")
    
    try:
        from app.services.storage.file_storage_service import FileStorageService
        
        print("开始创建存储服务...")
        storage = FileStorageService()
        
        print(f"存储服务创建成功:")
        print(f"  use_local_storage: {storage.use_local_storage}")
        print(f"  minio_client: {storage.minio_client}")
        
    except Exception as e:
        print(f"❌ 存储服务创建失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 调试MinIO配置问题...")
    print("=" * 50)
    
    debug_env_loading()
    debug_config_loading()
    debug_storage_service()
    
    print("=" * 50)
    print("🏁 调试完成")

if __name__ == "__main__":
    main()
