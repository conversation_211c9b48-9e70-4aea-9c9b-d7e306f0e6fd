"""
智能视频监控预警系统 - 统一启动脚本
支持一键配置和直接启动两种模式
"""
import os
import sys
import subprocess
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))


def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('app.log', encoding='utf-8')
        ]
    )


def run_command(command, description, check_error=True):
    """执行命令并显示进度"""
    print(f"🔄 {description}...")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True,
            encoding='utf-8'
        )
        
        if check_error and result.returncode != 0:
            print(f"❌ {description}失败:")
            print(f"   错误信息: {result.stderr}")
            return False
        else:
            print(f"✅ {description}完成")
            return True
            
    except Exception as e:
        print(f"❌ {description}异常: {e}")
        return False

def start_service():
    """启动服务"""
    print("🚀 启动智能视频监控预警系统...")
    
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("=" * 60)
    print("🎉 系统即将启动!")
    print("📊 Web界面: http://localhost:8000/docs")
    print("🔌 WebSocket: ws://localhost:8000/ws")
    print("📝 日志文件: app.log")
    print("=" * 60)
    print("按Ctrl+C停止服务\n")
    
    try:
        import uvicorn
        from app.main import app
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            reload=False,
            log_level="info"
        )
    except KeyboardInterrupt:
        logger.info("👋 收到停止信号，系统已关闭")
    except Exception as e:
        logger.error(f"启动失败: {e}")
        sys.exit(1)


def show_usage():
    """显示使用说明"""
    print("🎯 智能视频监控预警系统启动脚本")
    print("=" * 50)
    print("使用方法:")
    print("  python start.py          # 直接启动（推荐）")
    print("  python start.py --setup  # 完整配置+启动（首次使用）")
    print("  python start.py --help   # 显示帮助")
    print("=" * 50)


def main():
    """主函数"""
    args = sys.argv[1:] if len(sys.argv) > 1 else []
    
    # 显示帮助
    if "--help" in args or "-h" in args:
        show_usage()
        return
    
    # 完整配置模式（首次使用）
    if "--setup" in args:
        print("🏗️  智能视频监控预警系统 - 完整配置模式")

        start_service()
        
    else:
        # 快速启动模式（默认）
        print("🚀 智能视频监控预警系统 - 快速启动")
        print("=" * 60)
        
        # 基本检查
        env_file = Path(".env")
        if not env_file.exists():
            print("⚠️  未找到.env文件，建议运行完整配置:")
            print("   python start.py --setup")
            print("\n如需继续，请先配置:")
            print("   cp env.example .env")
            print("   # 然后编辑.env文件配置数据库等参数")
            return
        
        print("✅ 环境配置文件存在")
        start_service()


if __name__ == "__main__":
    main() 