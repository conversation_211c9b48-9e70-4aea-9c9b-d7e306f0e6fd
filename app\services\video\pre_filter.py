"""
预过滤处理器 - 三层优化架构的第一层

主要功能：
1. 基于运动检测的帧预筛选
2. 减少需要AI分析的帧数量
3. 性能统计和优化建议
4. 多级过滤策略
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Callable, Tuple
from dataclasses import dataclass, field
from enum import Enum
from threading import Lock
import numpy as np
import cv2

from .frame_buffer import VideoFrame, FrameBufferManager
from .motion_detector import (
    MotionDetectionManager, 
    MotionDetectionConfig, 
    MotionDetectionResult,
    MotionDetectionAlgorithm
)

logger = logging.getLogger(__name__)


class FilterDecision(Enum):
    """过滤决策"""
    SKIP = "skip"  # 跳过，不需要进一步处理
    PROCESS = "process"  # 需要进一步处理
    HIGH_PRIORITY = "high_priority"  # 高优先级处理
    BATCH_PROCESS = "batch_process"  # 批量处理


# 向后兼容的别名
FilterDecisionType = FilterDecision


@dataclass
class PreFilterConfig:
    """预过滤配置"""
    # 运动检测配置
    motion_config: MotionDetectionConfig = field(default_factory=MotionDetectionConfig)
    
    # 过滤策略
    enable_motion_filter: bool = True
    enable_time_filter: bool = True
    enable_similarity_filter: bool = True
    
    # 时间过滤参数
    min_frame_interval: float = 1.0  # 最小帧间隔（秒）
    max_frame_age: float = 30.0  # 最大帧存活时间（秒）
    
    # 相似度过滤参数
    similarity_threshold: float = 0.85  # 相似度阈值
    similarity_check_window: int = 5  # 相似度检查窗口大小
    
    # 批量处理参数
    batch_size: int = 5  # 批量处理大小
    batch_timeout: float = 2.0  # 批量超时时间（秒）
    
    # 性能优化参数
    skip_ratio_target: float = 0.7  # 目标跳过比例（70%的帧被过滤）
    performance_window: int = 100  # 性能统计窗口
    
    # 高优先级策略
    high_motion_threshold: float = 0.8  # 高运动阈值
    emergency_keywords: List[str] = field(default_factory=lambda: ["紧急", "危险", "异常"])


@dataclass
class PreFilterResult:
    """预过滤结果"""
    decision: FilterDecision
    confidence: float  # 决策置信度 (0.0-1.0)
    reason: str  # 决策原因
    motion_result: Optional[MotionDetectionResult] = None
    processing_time_ms: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PreFilterStatistics:
    """预过滤统计"""
    total_frames: int = 0
    skipped_frames: int = 0
    processed_frames: int = 0
    high_priority_frames: int = 0
    batch_processed_frames: int = 0
    
    skip_ratio: float = 0.0
    avg_processing_time_ms: float = 0.0
    total_processing_time_ms: float = 0.0
    
    motion_detected_frames: int = 0
    motion_detection_rate: float = 0.0
    
    performance_score: float = 0.0  # 性能分数 (0-100)
    cost_reduction_estimate: float = 0.0  # 成本节省估计 (0-1)


class FrameSimilarityAnalyzer:
    """帧相似度分析器"""
    
    def __init__(self, window_size: int = 5):
        self.window_size = window_size
        self._frame_hashes: Dict[str, List[np.ndarray]] = {}
        self._lock = Lock()
    
    def compute_frame_hash(self, frame: np.ndarray) -> np.ndarray:
        """计算帧哈希"""
        # 缩放到小尺寸进行快速比较
        small_frame = cv2.resize(frame, (64, 64))
        if len(small_frame.shape) == 3:
            small_frame = cv2.cvtColor(small_frame, cv2.COLOR_BGR2GRAY)
        
        # 计算感知哈希
        return cv2.resize(small_frame, (8, 8)).flatten()
    
    def calculate_similarity(self, hash1: np.ndarray, hash2: np.ndarray) -> float:
        """计算两个哈希的相似度"""
        return 1.0 - np.mean(np.abs(hash1 - hash2)) / 255.0
    
    def is_similar_to_recent(self, source_id: str, frame: np.ndarray, threshold: float = 0.85) -> Tuple[bool, float]:
        """检查帧是否与最近的帧相似"""
        with self._lock:
            frame_hash = self.compute_frame_hash(frame)
            
            if source_id not in self._frame_hashes:
                self._frame_hashes[source_id] = []
            
            recent_hashes = self._frame_hashes[source_id]
            
            # 检查与最近帧的相似度
            max_similarity = 0.0
            for recent_hash in recent_hashes:
                similarity = self.calculate_similarity(frame_hash, recent_hash)
                max_similarity = max(max_similarity, similarity)
            
            # 更新哈希历史
            recent_hashes.append(frame_hash)
            if len(recent_hashes) > self.window_size:
                recent_hashes.pop(0)
            
            is_similar = max_similarity >= threshold
            return is_similar, max_similarity


class PreFilterProcessor:
    """预过滤处理器"""
    
    def __init__(self, config: PreFilterConfig):
        self.config = config
        
        # 初始化组件
        self._motion_manager = MotionDetectionManager(config.motion_config)
        self._similarity_analyzer = FrameSimilarityAnalyzer(config.similarity_check_window)
        
        # 统计信息
        self._statistics: Dict[str, PreFilterStatistics] = {}
        self._last_process_time: Dict[str, float] = {}
        
        # 批量处理
        self._batch_queues: Dict[str, List[VideoFrame]] = {}
        self._batch_timers: Dict[str, float] = {}
        
        # 线程锁
        self._lock = Lock()
        
        logger.info("预过滤处理器已初始化")
    
    def add_source(self, source_id: str, motion_config: Optional[MotionDetectionConfig] = None):
        """添加视频源"""
        with self._lock:
            # 添加运动检测器
            config = motion_config or self.config.motion_config
            self._motion_manager.add_detector(source_id, config)
            
            # 初始化统计信息
            self._statistics[source_id] = PreFilterStatistics()
            self._last_process_time[source_id] = 0.0
            
            # 初始化批量处理队列
            self._batch_queues[source_id] = []
            self._batch_timers[source_id] = time.time()
            
            logger.info(f"视频源已添加到预过滤器 - 源: {source_id}")
    
    def remove_source(self, source_id: str):
        """移除视频源"""
        with self._lock:
            # 移除运动检测器
            self._motion_manager.remove_detector(source_id)
            
            # 清理相关数据
            self._statistics.pop(source_id, None)
            self._last_process_time.pop(source_id, None)
            self._batch_queues.pop(source_id, None)
            self._batch_timers.pop(source_id, None)
            
            logger.info(f"视频源已从预过滤器移除 - 源: {source_id}")
    
    async def process_frame(self, frame: VideoFrame) -> PreFilterResult:
        """处理单个帧"""
        start_time = time.time()
        source_id = frame.source_id
        
        try:
            # 确保源已添加
            if source_id not in self._statistics:
                self.add_source(source_id)
            
            # 更新统计
            with self._lock:
                stats = self._statistics[source_id]
                stats.total_frames += 1
            
            # 1. 时间过滤
            if self.config.enable_time_filter:
                time_result = self._apply_time_filter(frame)
                if time_result.decision == FilterDecision.SKIP:
                    self._update_statistics(source_id, time_result, start_time)
                    return time_result
            
            # 2. 运动检测过滤
            motion_result = None
            if self.config.enable_motion_filter:
                motion_result = self._motion_manager.detect_motion(source_id, frame)
                if motion_result:
                    motion_filter_result = self._apply_motion_filter(frame, motion_result)
                    if motion_filter_result.decision != FilterDecision.PROCESS:
                        self._update_statistics(source_id, motion_filter_result, start_time)
                        return motion_filter_result
            
            # 3. 相似度过滤
            if self.config.enable_similarity_filter:
                similarity_result = self._apply_similarity_filter(frame)
                if similarity_result.decision == FilterDecision.SKIP:
                    self._update_statistics(source_id, similarity_result, start_time)
                    return similarity_result
            
            # 4. 决定处理策略
            final_result = self._make_final_decision(frame, motion_result)
            self._update_statistics(source_id, final_result, start_time)
            
            return final_result
            
        except Exception as e:
            logger.error(f"预过滤处理失败 - 源: {source_id}, 错误: {e}")
            # 返回默认处理决策
            result = PreFilterResult(
                decision=FilterDecision.PROCESS,
                confidence=0.5,
                reason=f"处理失败，默认处理: {str(e)}",
                processing_time_ms=(time.time() - start_time) * 1000,
                metadata={"error": str(e)}
            )
            self._update_statistics(source_id, result, start_time)
            return result
    
    def _apply_time_filter(self, frame: VideoFrame) -> PreFilterResult:
        """应用时间过滤"""
        source_id = frame.source_id
        current_time = time.time()
        last_time = self._last_process_time.get(source_id, 0.0)
        
        # 检查帧间隔
        if current_time - last_time < self.config.min_frame_interval:
            return PreFilterResult(
                decision=FilterDecision.SKIP,
                confidence=0.9,
                reason=f"帧间隔过短: {current_time - last_time:.2f}s < {self.config.min_frame_interval}s"
            )
        
        # 检查帧年龄
        frame_age = current_time - frame.timestamp
        if frame_age > self.config.max_frame_age:
            return PreFilterResult(
                decision=FilterDecision.SKIP,
                confidence=0.95,
                reason=f"帧过期: {frame_age:.2f}s > {self.config.max_frame_age}s"
            )
        
        return PreFilterResult(
            decision=FilterDecision.PROCESS,
            confidence=0.8,
            reason="时间过滤通过"
        )
    
    def _apply_motion_filter(self, frame: VideoFrame, motion_result: MotionDetectionResult) -> PreFilterResult:
        """应用运动检测过滤"""
        # 高运动优先级
        if motion_result.motion_score >= self.config.high_motion_threshold:
            return PreFilterResult(
                decision=FilterDecision.HIGH_PRIORITY,
                confidence=0.95,
                reason=f"高运动强度: {motion_result.motion_score:.3f}",
                motion_result=motion_result
            )
        
        # 显著运动处理
        if motion_result.has_significant_motion:
            return PreFilterResult(
                decision=FilterDecision.PROCESS,
                confidence=0.8,
                reason=f"检测到显著运动: {motion_result.motion_score:.3f}",
                motion_result=motion_result
            )
        
        # 无显著运动，跳过
        return PreFilterResult(
            decision=FilterDecision.SKIP,
            confidence=0.85,
            reason=f"无显著运动: {motion_result.motion_score:.3f}",
            motion_result=motion_result
        )
    
    def _apply_similarity_filter(self, frame: VideoFrame) -> PreFilterResult:
        """应用相似度过滤"""
        try:
            is_similar, similarity_score = self._similarity_analyzer.is_similar_to_recent(
                frame.source_id, 
                frame.frame_data, 
                self.config.similarity_threshold
            )
            
            if is_similar:
                return PreFilterResult(
                    decision=FilterDecision.SKIP,
                    confidence=0.8,
                    reason=f"与最近帧相似度过高: {similarity_score:.3f}",
                    metadata={"similarity_score": similarity_score}
                )
            
            return PreFilterResult(
                decision=FilterDecision.PROCESS,
                confidence=0.7,
                reason=f"帧内容有变化: 相似度 {similarity_score:.3f}",
                metadata={"similarity_score": similarity_score}
            )
            
        except Exception as e:
            logger.warning(f"相似度分析失败 - 源: {frame.source_id}, 错误: {e}")
            return PreFilterResult(
                decision=FilterDecision.PROCESS,
                confidence=0.5,
                reason="相似度分析失败，默认处理"
            )
    
    def _make_final_decision(self, frame: VideoFrame, motion_result: Optional[MotionDetectionResult]) -> PreFilterResult:
        """做出最终处理决策"""
        # 检查是否应该批量处理
        if self._should_batch_process(frame.source_id):
            return PreFilterResult(
                decision=FilterDecision.BATCH_PROCESS,
                confidence=0.7,
                reason="符合批量处理条件",
                motion_result=motion_result
            )
        
        # 默认单独处理
        return PreFilterResult(
            decision=FilterDecision.PROCESS,
            confidence=0.8,
            reason="通过所有过滤器，需要处理",
            motion_result=motion_result
        )
    
    def _should_batch_process(self, source_id: str) -> bool:
        """判断是否应该批量处理"""
        with self._lock:
            queue = self._batch_queues.get(source_id, [])
            timer = self._batch_timers.get(source_id, time.time())
            
            # 检查批量大小或超时
            return (len(queue) >= self.config.batch_size or 
                    time.time() - timer >= self.config.batch_timeout)
    
    def _update_statistics(self, source_id: str, result: PreFilterResult, start_time: float):
        """更新统计信息"""
        with self._lock:
            stats = self._statistics.get(source_id)
            if not stats:
                return
            
            # 更新处理时间
            processing_time = (time.time() - start_time) * 1000
            result.processing_time_ms = processing_time
            stats.total_processing_time_ms += processing_time
            
            # 更新决策统计
            if result.decision == FilterDecision.SKIP:
                stats.skipped_frames += 1
            elif result.decision == FilterDecision.PROCESS:
                stats.processed_frames += 1
                self._last_process_time[source_id] = time.time()
            elif result.decision == FilterDecision.HIGH_PRIORITY:
                stats.high_priority_frames += 1
                self._last_process_time[source_id] = time.time()
            elif result.decision == FilterDecision.BATCH_PROCESS:
                stats.batch_processed_frames += 1
            
            # 更新运动检测统计
            if result.motion_result and result.motion_result.has_significant_motion:
                stats.motion_detected_frames += 1
            
            # 计算比例和平均值
            if stats.total_frames > 0:
                stats.skip_ratio = stats.skipped_frames / stats.total_frames
                stats.avg_processing_time_ms = stats.total_processing_time_ms / stats.total_frames
                stats.motion_detection_rate = stats.motion_detected_frames / stats.total_frames
            
            # 计算性能分数
            stats.performance_score = self._calculate_performance_score(stats)
            stats.cost_reduction_estimate = min(stats.skip_ratio, 0.9)  # 最多90%的成本节省
    
    def _calculate_performance_score(self, stats: PreFilterStatistics) -> float:
        """计算性能分数"""
        if stats.total_frames == 0:
            return 0.0
        
        # 基于跳过比例的分数（目标70%）
        skip_score = min(stats.skip_ratio / self.config.skip_ratio_target, 1.0) * 40
        
        # 基于处理时间的分数
        time_score = max(0, 100 - stats.avg_processing_time_ms / 10) * 0.3
        
        # 基于运动检测准确性的分数
        motion_score = min(stats.motion_detection_rate * 2, 1.0) * 30
        
        return min(skip_score + time_score + motion_score, 100.0)
    
    def get_statistics(self, source_id: Optional[str] = None) -> Dict[str, PreFilterStatistics]:
        """获取统计信息"""
        with self._lock:
            if source_id:
                return {source_id: self._statistics.get(source_id, PreFilterStatistics())}
            else:
                return dict(self._statistics)
    
    def get_overall_statistics(self) -> PreFilterStatistics:
        """获取总体统计信息"""
        with self._lock:
            overall = PreFilterStatistics()
            
            for stats in self._statistics.values():
                overall.total_frames += stats.total_frames
                overall.skipped_frames += stats.skipped_frames
                overall.processed_frames += stats.processed_frames
                overall.high_priority_frames += stats.high_priority_frames
                overall.batch_processed_frames += stats.batch_processed_frames
                overall.motion_detected_frames += stats.motion_detected_frames
                overall.total_processing_time_ms += stats.total_processing_time_ms
            
            # 计算总体比例
            if overall.total_frames > 0:
                overall.skip_ratio = overall.skipped_frames / overall.total_frames
                overall.avg_processing_time_ms = overall.total_processing_time_ms / overall.total_frames
                overall.motion_detection_rate = overall.motion_detected_frames / overall.total_frames
                overall.performance_score = self._calculate_performance_score(overall)
                overall.cost_reduction_estimate = min(overall.skip_ratio, 0.9)
            
            return overall
    
    def reset_statistics(self, source_id: Optional[str] = None):
        """重置统计信息"""
        with self._lock:
            if source_id:
                if source_id in self._statistics:
                    self._statistics[source_id] = PreFilterStatistics()
                    logger.info(f"预过滤统计已重置 - 源: {source_id}")
            else:
                for source_id in self._statistics:
                    self._statistics[source_id] = PreFilterStatistics()
                logger.info("所有预过滤统计已重置")
    
    async def cleanup(self):
        """清理资源"""
        with self._lock:
            self._statistics.clear()
            self._last_process_time.clear()
            self._batch_queues.clear()
            self._batch_timers.clear()
        
        logger.info("预过滤处理器已清理") 