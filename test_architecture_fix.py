#!/usr/bin/env python3
"""
测试架构修复
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_database_session():
    """测试数据库会话创建"""
    print("🔧 测试数据库会话创建...")
    
    try:
        # 加载环境变量
        from dotenv import load_dotenv
        load_dotenv()
        
        # 测试新的create_session函数
        from app.database import create_session
        session = await create_session()
        print("✅ 数据库会话创建成功")
        
        # 测试会话是否可用
        result = await session.execute("SELECT 1")
        print("✅ 数据库会话可用")
        
        # 关闭会话
        await session.close()
        print("✅ 数据库会话关闭成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库会话测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_repository_creation():
    """测试Repository创建"""
    print("\n🔧 测试Repository创建...")
    
    try:
        from app.database import create_session
        from app.repositories.alert_event_repository import AlertEventRepository
        from app.repositories.camera_repository import CameraRepository
        
        session = await create_session()
        
        # 测试Repository创建
        alert_repo = AlertEventRepository(session)
        camera_repo = CameraRepository(session)
        
        print("✅ Repository创建成功")
        
        await session.close()
        return True
        
    except Exception as e:
        print(f"❌ Repository创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_alert_service():
    """测试AlertService初始化"""
    print("\n🔧 测试AlertService初始化...")
    
    try:
        from app.services.alert.alert_service import AlertService
        from app.services.storage.file_storage_service import FileStorageService
        from app.services.notification.websocket_service import WebSocketService
        
        storage_service = FileStorageService()
        websocket_service = WebSocketService()
        
        # 测试新的AlertService初始化
        alert_service = AlertService(
            storage_service=storage_service,
            websocket_service=websocket_service
        )
        print("✅ AlertService初始化成功")
        
        # 测试Repository获取
        repos = await alert_service._get_repositories()
        print("✅ Repository动态获取成功")
        
        # 关闭会话
        await repos['session'].close()
        
        return True
        
    except Exception as e:
        print(f"❌ AlertService测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_main_import():
    """测试主模块导入"""
    print("\n🔧 测试主模块导入...")
    
    try:
        import app.main
        print("✅ 主模块导入成功")
        return True
        
    except Exception as e:
        print(f"❌ 主模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🚀 测试架构修复...")
    print("=" * 60)
    
    tests = [
        ("数据库会话创建", test_database_session),
        ("Repository创建", test_repository_creation),
        ("AlertService初始化", test_alert_service),
        ("主模块导入", test_main_import),
    ]
    
    results = []
    for test_name, test_func in tests:
        result = await test_func()
        results.append((test_name, result))
    
    print("=" * 60)
    print("📊 测试结果:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 架构修复成功!")
        print("现在可以启动系统: python start.py")
        print("\n💡 修复说明:")
        print("- 创建了 create_session() 函数用于服务层")
        print("- 重新设计了 AlertService 使用动态Repository")
        print("- 修复了所有Repository的构造函数参数顺序")
        print("- 保持了API路由的依赖注入模式不变")
    else:
        print("\n💥 架构修复失败，需要进一步检查")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    import asyncio
    sys.exit(asyncio.run(main()))
