#!/usr/bin/env python3
"""
测试Repository修复
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_repositories():
    """测试所有Repository初始化"""
    print("🔧 测试Repository初始化...")
    
    try:
        # 加载环境变量
        from dotenv import load_dotenv
        load_dotenv()
        
        # 获取数据库会话
        from app.database import get_session
        db_session = await get_session()
        print("✅ 数据库会话获取成功")
        
        # 测试所有Repository
        repositories = [
            ("AlertEventRepository", "app.repositories.alert_event_repository", "AlertEventRepository"),
            ("CameraRepository", "app.repositories.camera_repository", "CameraRepository"),
            ("StoreRepository", "app.repositories.store_repository", "StoreRepository"),
            ("AnomalyBehaviorRepository", "app.repositories.anomaly_behavior_repository", "AnomalyBehaviorRepository"),
            ("AnalysisRuleRepository", "app.repositories.analysis_rule_repository", "AnalysisRuleRepository"),
            ("RuleBehaviorMappingRepository", "app.repositories.rule_behavior_mapping_repository", "RuleBehaviorMappingRepository"),
            ("PromptTemplateRepository", "app.repositories.prompt_template_repository", "PromptTemplateRepository"),
            ("EvidenceFileRepository", "app.repositories.evidence_file_repository", "EvidenceFileRepository"),
            ("ApiKeyRepository", "app.repositories.api_key_repository", "ApiKeyRepository"),
        ]
        
        for repo_name, module_path, class_name in repositories:
            try:
                module = __import__(module_path, fromlist=[class_name])
                repo_class = getattr(module, class_name)
                repo_instance = repo_class(db_session)
                print(f"✅ {repo_name} 初始化成功")
            except Exception as e:
                print(f"❌ {repo_name} 初始化失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Repository测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_alert_service():
    """测试AlertService初始化"""
    print("\n🔧 测试AlertService初始化...")
    
    try:
        # 获取数据库会话
        from app.database import get_session
        db_session = await get_session()
        
        # 测试AlertService
        from app.services.alert.alert_service import AlertService
        from app.services.storage.file_storage_service import FileStorageService
        from app.services.notification.websocket_service import WebSocketService
        
        storage_service = FileStorageService()
        websocket_service = WebSocketService()
        
        alert_service = AlertService(
            db_session=db_session,
            storage_service=storage_service,
            websocket_service=websocket_service
        )
        print("✅ AlertService初始化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ AlertService测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🚀 测试Repository和AlertService修复...")
    print("=" * 60)
    
    tests = [
        ("Repository初始化", test_repositories),
        ("AlertService初始化", test_alert_service),
    ]
    
    results = []
    for test_name, test_func in tests:
        result = await test_func()
        results.append((test_name, result))
    
    print("=" * 60)
    print("📊 测试结果:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 Repository和AlertService修复成功!")
        print("现在可以启动系统: python start.py")
    else:
        print("\n💥 修复失败，需要进一步检查")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    import asyncio
    sys.exit(asyncio.run(main()))
