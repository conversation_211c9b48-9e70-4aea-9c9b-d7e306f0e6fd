"""
WebSocket服务 - 实时预警消息推送
"""
import asyncio
import json
import logging
from typing import Dict, List, Any, Set
from datetime import datetime
from weakref import WeakSet

from fastapi import WebSocket, WebSocketDisconnect
from starlette.websockets import WebSocketState

from app.core.config import settings
from app.core.exceptions import WebSocketException

logger = logging.getLogger(__name__)


class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 活跃连接
        self.active_connections: Set[WebSocket] = set()
        
        # 连接信息映射
        self.connection_info: Dict[WebSocket, Dict[str, Any]] = {}
        
        # 订阅管理
        self.subscriptions: Dict[str, Set[WebSocket]] = {
            "alerts": set(),
            "camera_status": set(),
            "system_status": set()
        }
    
    async def connect(self, websocket: WebSocket, client_info: Dict[str, Any] = None):
        """建立WebSocket连接"""
        try:
            await websocket.accept()
            
            self.active_connections.add(websocket)
            self.connection_info[websocket] = {
                "client_info": client_info or {},
                "connected_at": datetime.now(),
                "subscriptions": set()
            }
            
            logger.info(f"新WebSocket连接: {websocket.client.host}:{websocket.client.port}")
            
            # 发送连接成功消息
            await self.send_personal_message(websocket, {
                "type": "connection_established",
                "message": "WebSocket连接已建立",
                "timestamp": datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"WebSocket连接失败: {str(e)}")
            raise WebSocketException(f"连接失败: {str(e)}")
    
    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        try:
            if websocket in self.active_connections:
                self.active_connections.remove(websocket)
            
            # 清理订阅
            if websocket in self.connection_info:
                subscriptions = self.connection_info[websocket].get("subscriptions", set())
                for sub_type in subscriptions:
                    if sub_type in self.subscriptions:
                        self.subscriptions[sub_type].discard(websocket)
                
                del self.connection_info[websocket]
            
            logger.info(f"WebSocket连接已断开: {websocket.client.host if websocket.client else 'unknown'}")
            
        except Exception as e:
            logger.error(f"断开WebSocket连接异常: {str(e)}")
    
    async def send_personal_message(self, websocket: WebSocket, message: Dict[str, Any]):
        """发送个人消息"""
        try:
            if websocket.client_state == WebSocketState.CONNECTED:
                await websocket.send_text(json.dumps(message, ensure_ascii=False))
        except Exception as e:
            logger.error(f"发送个人消息失败: {str(e)}")
            self.disconnect(websocket)
    
    async def broadcast(self, message: Dict[str, Any], subscription_type: str = None):
        """广播消息"""
        if subscription_type and subscription_type in self.subscriptions:
            # 发送给特定订阅类型的连接
            target_connections = self.subscriptions[subscription_type].copy()
        else:
            # 发送给所有连接
            target_connections = self.active_connections.copy()
        
        disconnected_connections = []
        
        for connection in target_connections:
            try:
                if connection.client_state == WebSocketState.CONNECTED:
                    await connection.send_text(json.dumps(message, ensure_ascii=False))
                else:
                    disconnected_connections.append(connection)
            except Exception as e:
                logger.error(f"广播消息失败: {str(e)}")
                disconnected_connections.append(connection)
        
        # 清理断开的连接
        for connection in disconnected_connections:
            self.disconnect(connection)
        
        if target_connections:
            logger.debug(f"广播消息给 {len(target_connections)} 个连接")
    
    async def subscribe(self, websocket: WebSocket, subscription_type: str):
        """订阅特定类型的消息"""
        if subscription_type not in self.subscriptions:
            self.subscriptions[subscription_type] = set()
        
        self.subscriptions[subscription_type].add(websocket)
        
        if websocket in self.connection_info:
            self.connection_info[websocket]["subscriptions"].add(subscription_type)
        
        logger.debug(f"WebSocket订阅: {subscription_type}")
        
        # 发送订阅确认
        await self.send_personal_message(websocket, {
            "type": "subscription_confirmed",
            "subscription_type": subscription_type,
            "message": f"已订阅 {subscription_type} 消息",
            "timestamp": datetime.now().isoformat()
        })
    
    async def unsubscribe(self, websocket: WebSocket, subscription_type: str):
        """取消订阅"""
        if subscription_type in self.subscriptions:
            self.subscriptions[subscription_type].discard(websocket)
        
        if websocket in self.connection_info:
            self.connection_info[websocket]["subscriptions"].discard(subscription_type)
        
        logger.debug(f"WebSocket取消订阅: {subscription_type}")
        
        # 发送取消订阅确认
        await self.send_personal_message(websocket, {
            "type": "unsubscription_confirmed",
            "subscription_type": subscription_type,
            "message": f"已取消订阅 {subscription_type} 消息",
            "timestamp": datetime.now().isoformat()
        })
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        return {
            "total_connections": len(self.active_connections),
            "subscriptions": {
                sub_type: len(connections) 
                for sub_type, connections in self.subscriptions.items()
            },
            "timestamp": datetime.now().isoformat()
        }


class WebSocketService:
    """WebSocket服务"""
    
    def __init__(self):
        self.manager = ConnectionManager()
        self._heartbeat_task = None
    
    async def start_heartbeat(self):
        """启动心跳任务"""
        if self._heartbeat_task is None:
            self._start_heartbeat()
    
    async def handle_websocket(self, websocket: WebSocket, client_info: Dict[str, Any] = None):
        """处理WebSocket连接"""
        # 确保心跳任务已启动
        await self.start_heartbeat()
        
        await self.manager.connect(websocket, client_info)
        
        try:
            while True:
                # 接收客户端消息
                try:
                    data = await websocket.receive_text()
                    message = json.loads(data)
                    await self._handle_client_message(websocket, message)
                except json.JSONDecodeError:
                    await self.manager.send_personal_message(websocket, {
                        "type": "error",
                        "message": "无效的JSON消息格式",
                        "timestamp": datetime.now().isoformat()
                    })
                except WebSocketDisconnect:
                    break
                    
        except WebSocketDisconnect:
            pass
        except Exception as e:
            logger.error(f"WebSocket处理异常: {str(e)}")
        finally:
            self.manager.disconnect(websocket)
    
    async def _handle_client_message(self, websocket: WebSocket, message: Dict[str, Any]):
        """处理客户端消息"""
        message_type = message.get("type", "")
        
        if message_type == "subscribe":
            subscription_type = message.get("subscription_type", "")
            if subscription_type:
                await self.manager.subscribe(websocket, subscription_type)
            else:
                await self.manager.send_personal_message(websocket, {
                    "type": "error",
                    "message": "缺少subscription_type参数",
                    "timestamp": datetime.now().isoformat()
                })
        
        elif message_type == "unsubscribe":
            subscription_type = message.get("subscription_type", "")
            if subscription_type:
                await self.manager.unsubscribe(websocket, subscription_type)
        
        elif message_type == "ping":
            # 心跳响应
            await self.manager.send_personal_message(websocket, {
                "type": "pong",
                "timestamp": datetime.now().isoformat()
            })
        
        elif message_type == "get_stats":
            # 获取连接统计
            stats = self.manager.get_connection_stats()
            await self.manager.send_personal_message(websocket, {
                "type": "stats",
                "data": stats,
                "timestamp": datetime.now().isoformat()
            })
        
        else:
            await self.manager.send_personal_message(websocket, {
                "type": "error",
                "message": f"未知的消息类型: {message_type}",
                "timestamp": datetime.now().isoformat()
            })
    
    async def broadcast_alert(self, alert_data: Dict[str, Any]):
        """广播预警消息"""
        message = {
            "type": "alert",
            "data": alert_data,
            "timestamp": datetime.now().isoformat()
        }
        
        await self.manager.broadcast(message, "alerts")
        logger.info(f"广播预警消息: {alert_data.get('title', '未知预警')}")
    
    async def broadcast_camera_status(self, camera_status: Dict[str, Any]):
        """广播摄像头状态"""
        message = {
            "type": "camera_status",
            "data": camera_status,
            "timestamp": datetime.now().isoformat()
        }
        
        await self.manager.broadcast(message, "camera_status")
    
    async def broadcast_system_status(self, system_status: Dict[str, Any]):
        """广播系统状态"""
        message = {
            "type": "system_status",
            "data": system_status,
            "timestamp": datetime.now().isoformat()
        }
        
        await self.manager.broadcast(message, "system_status")
    
    async def send_custom_message(self, message_type: str, data: Dict[str, Any], subscription_type: str = None):
        """发送自定义消息"""
        message = {
            "type": message_type,
            "data": data,
            "timestamp": datetime.now().isoformat()
        }
        
        await self.manager.broadcast(message, subscription_type)
    
    def _start_heartbeat(self):
        """启动心跳任务"""
        async def heartbeat_task():
            while True:
                try:
                    await asyncio.sleep(30)  # 每30秒发送心跳
                    
                    # 发送心跳消息
                    await self.manager.broadcast({
                        "type": "heartbeat",
                        "timestamp": datetime.now().isoformat(),
                        "server_status": "healthy"
                    })
                    
                except Exception as e:
                    logger.error(f"心跳任务异常: {str(e)}")
        
        self._heartbeat_task = asyncio.create_task(heartbeat_task())
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        return self.manager.get_connection_stats()
    
    async def close(self):
        """关闭WebSocket服务"""
        if self._heartbeat_task:
            self._heartbeat_task.cancel()
            try:
                await self._heartbeat_task
            except asyncio.CancelledError:
                pass
        
        # 断开所有连接
        connections = self.manager.active_connections.copy()
        for connection in connections:
            try:
                await connection.close()
            except Exception as e:
                logger.error(f"关闭WebSocket连接异常: {str(e)}")
            finally:
                self.manager.disconnect(connection)
        
        logger.info("WebSocket服务已关闭")


# 全局WebSocket服务实例
websocket_service = WebSocketService() 