"""
全局配置管理
"""
import os
from typing import Any, Dict, Optional, List, Union
from pydantic import Field, field_validator, ConfigDict
from pydantic_settings import BaseSettings, SettingsConfigDict
from pathlib import Path


class DatabaseSettings(BaseSettings):
    """数据库配置"""
    host: str = Field(default="localhost", env="DB_HOST")
    port: int = Field(default=3306, env="DB_PORT")
    username: str = Field(default="root", env="DB_USER")  # 修正映射
    password: str = Field(default="", env="DB_PASSWORD")
    database: str = Field(default="video_ai_analysis", env="DB_NAME")  # 修正映射
    charset: str = Field(default="utf8mb4", env="DB_CHARSET")
    
    # 连接池配置
    pool_size: int = Field(default=20, env="DB_POOL_SIZE")
    max_overflow: int = Field(default=30, env="DB_MAX_OVERFLOW")
    pool_timeout: int = Field(default=30, env="DB_POOL_TIMEOUT")
    pool_recycle: int = Field(default=3600, env="DB_POOL_RECYCLE")

    model_config = ConfigDict(extra="ignore")
    
    @property
    def url(self) -> str:
        """数据库连接URL"""
        return f"mysql+aiomysql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}?charset={self.charset}"


class RedisSettings(BaseSettings):
    """Redis配置"""
    host: str = Field(default="localhost", env="REDIS_HOST")
    port: int = Field(default=6379, env="REDIS_PORT")
    password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    database: int = Field(default=0, env="REDIS_DB")  # 修正映射
    
    # 连接池配置
    max_connections: int = Field(default=100, env="REDIS_POOL_SIZE")  # 修正映射
    timeout: int = Field(default=5, env="REDIS_TIMEOUT")

    model_config = ConfigDict(extra="ignore")
    
    @property
    def url(self) -> str:
        """Redis连接URL"""
        auth = f":{self.password}@" if self.password else ""
        return f"redis://{auth}{self.host}:{self.port}/{self.database}"


class MinIOSettings(BaseSettings):
    """MinIO配置"""
    endpoint: str = Field(default="*************:9000", env="MINIO_ENDPOINT")
    access_key: str = Field(default="v3Di4s9aszZC6DzeNqHa", env="MINIO_ACCESS_KEY")
    secret_key: str = Field(default="YAXnJlXazQsjPvsJhZk9ow3qRfvl8Gglu7P8OpHJ", env="MINIO_SECRET_KEY")
    secure: bool = Field(default=False, env="MINIO_SECURE")
    
    # 存储桶配置
    bucket_name: str = Field(default="difybn", env="MINIO_BUCKET_NAME")
    region: str = Field(default="us-east-1", env="MINIO_REGION")

    model_config = ConfigDict(extra="ignore")


class AISettings(BaseSettings):
    """AI服务配置"""
    # 通义千问配置
    qwen_api_key: str = Field(default="", env="AI_API_KEY")  # 修正映射
    qwen_api_secret: str = Field(default="", env="QWEN_API_SECRET")
    qwen_model: str = Field(default="qwen-omni-turbo", env="AI_MODEL_NAME")  # 修正映射
    # https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions
    qwen_base_url: str = Field(default="https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions", env="QWEN_BASE_URL")
    
    # API调用配置
    max_retries: int = Field(default=3, env="AI_MAX_RETRIES")
    timeout: int = Field(default=30, env="AI_TIMEOUT")
    max_concurrent_requests: int = Field(default=10, env="AI_MAX_CONCURRENT")  # 修正映射
    
    # 性能优化配置
    batch_size: int = Field(default=5, env="BATCH_PROCESSING_SIZE")  # 修正映射
    cooldown_seconds: int = Field(default=1, env="AI_COOLDOWN_SECONDS")

    model_config = ConfigDict(extra="ignore")


class VideoSettings(BaseSettings):
    """视频处理配置"""
    # 视频流配置
    max_concurrent_streams: int = Field(default=100, env="VIDEO_MAX_STREAMS")  # 修正映射
    rtsp_timeout: int = Field(default=10, env="VIDEO_TIMEOUT")  # 修正映射
    frame_buffer_size: int = Field(default=30, env="VIDEO_BUFFER_SIZE")  # 修正映射
    
    # 帧处理配置
    default_fps: float = Field(default=25.0, env="VIDEO_DEFAULT_FPS")
    analysis_fps: float = Field(default=1.0, env="VIDEO_FRAME_RATE")  # 修正映射
    motion_threshold: float = Field(default=0.3, env="VIDEO_MOTION_THRESHOLD")
    
    # 帧缓冲配置
    frame_max_age: float = Field(default=30.0, env="VIDEO_FRAME_MAX_AGE")  # 帧最大存活时间（秒）
    max_memory_mb: float = Field(default=1024.0, env="VIDEO_MAX_MEMORY_MB")  # 最大内存使用（MB）
    
    # 性能优化配置
    enable_motion_detection: bool = Field(default=True, env="VIDEO_ENABLE_MOTION_DETECTION")
    frame_skip_threshold: int = Field(default=5, env="VIDEO_FRAME_SKIP_THRESHOLD")
    
    # 存储配置
    video_save_path: str = Field(default="./storage/videos", env="VIDEO_SAVE_PATH")
    image_save_path: str = Field(default="./storage/images", env="IMAGE_SAVE_PATH")

    model_config = ConfigDict(extra="ignore")


class ServerSettings(BaseSettings):
    """服务器配置"""
    host: str = Field(default="0.0.0.0", env="HOST")  # 修正映射
    port: int = Field(default=8000, env="PORT")  # 修正映射
    debug: bool = Field(default=False, env="DEBUG")  # 修正映射
    reload: bool = Field(default=False, env="RELOAD")  # 修正映射

    # 安全配置
    secret_key: str = Field(default="video-ai-analysis-secret-key", env="SECRET_KEY")  # 修正映射
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")  # 修正映射

    # CORS配置 - 使用字符串类型避免JSON解析问题
    cors_origins_str: str = Field(default="*", env="CORS_ORIGINS")
    cors_methods: List[str] = Field(default=["*"], env="SERVER_CORS_METHODS")
    cors_headers: List[str] = Field(default=["*"], env="SERVER_CORS_HEADERS")

    model_config = ConfigDict(extra="ignore")

    @property
    def cors_origins(self) -> List[str]:
        """解析CORS origins"""
        if self.cors_origins_str == "*":
            return ["*"]
        return [origin.strip() for origin in self.cors_origins_str.split(",") if origin.strip()]


class LoggingSettings(BaseSettings):
    """日志配置"""
    level: str = Field(default="INFO", env="LOG_LEVEL")
    format: str = Field(default="%(asctime)s - %(name)s - %(levelname)s - %(message)s", env="LOG_FORMAT")
    file_path: str = Field(default="./logs/app.log", env="LOG_FILE_PATH")
    max_bytes: int = Field(default=10485760, env="LOG_MAX_BYTES")  # 10MB
    backup_count: int = Field(default=5, env="LOG_BACKUP_COUNT")
    
    # 结构化日志配置
    enable_json_format: bool = Field(default=False, env="LOG_ENABLE_JSON_FORMAT")
    enable_file_logging: bool = Field(default=True, env="LOG_ENABLE_FILE_LOGGING")
    enable_console_logging: bool = Field(default=True, env="LOG_ENABLE_CONSOLE_LOGGING")

    model_config = ConfigDict(extra="ignore")


class Settings(BaseSettings):
    """主配置类"""
    # 环境配置
    environment: str = Field(default="development", env="ENVIRONMENT")
    app_name: str = Field(default="智能视频监控预警系统", env="PROJECT_NAME")
    app_version: str = Field(default="1.0.0", env="VERSION")
    app_description: str = Field(default="智能视频监控预警系统 - 基于AI的实时视频分析服务", env="APP_DESCRIPTION")
    
    # 各模块配置
    database: DatabaseSettings = DatabaseSettings()
    redis: RedisSettings = RedisSettings()
    minio: MinIOSettings = MinIOSettings()
    ai: AISettings = AISettings()
    video: VideoSettings = VideoSettings()
    server: ServerSettings = ServerSettings()
    logging: LoggingSettings = LoggingSettings()
    
    # 监控配置
    enable_monitoring: bool = Field(default=True, env="METRIC_COLLECTION_ENABLED")
    health_check_interval: int = Field(default=30, env="HEALTH_CHECK_INTERVAL")
    
    # 性能配置
    enable_performance_logging: bool = Field(default=True, env="ENABLE_PERFORMANCE_LOGGING")
    performance_threshold_ms: int = Field(default=1000, env="PERFORMANCE_THRESHOLD_MS")
    
    model_config = ConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"  # 忽略额外字段
    )
        
    @field_validator("environment")
    @classmethod
    def validate_environment(cls, v):
        """验证环境配置"""
        allowed_envs = ["development", "testing", "production"]
        if v not in allowed_envs:
            raise ValueError(f"Environment must be one of {allowed_envs}")
        return v
    
    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.environment == "development"
    
    @property
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.environment == "testing"
    
    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.environment == "production"


# 全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings 