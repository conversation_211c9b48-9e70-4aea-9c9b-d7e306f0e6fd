"""
异常行为定义数据访问层
提供异常行为的定义和配置管理
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from ..database.base_repository import BaseRepository
from ..models.anomaly_behavior import AnomalyBehavior
from ..core.logger import logger


class AnomalyBehaviorRepository(BaseRepository[AnomalyBehavior]):
    """异常行为Repository"""
    
    def __init__(self, db: Session):
        super().__init__(AnomalyBehavior, db)
    
    async def get_behavior_by_code(self, code: str) -> Optional[AnomalyBehavior]:
        """根据行为代码获取异常行为"""
        return await self.find_by_field("code", code)
    
    async def get_enabled_behaviors(self) -> List[AnomalyBehavior]:
        """获取所有启用的异常行为"""
        return await self.find_by_conditions([
            AnomalyBehavior.is_enabled == True,
            AnomalyBehavior.deleted == False
        ])
    
    async def get_behaviors_by_category(self, category: str) -> List[AnomalyBehavior]:
        """根据分类获取异常行为"""
        return await self.find_by_conditions([
            AnomalyBehavior.category == category,
            AnomalyBehavior.is_enabled == True,
            AnomalyBehavior.deleted == False
        ])
    
    async def get_behaviors_by_severity(self, severity: str) -> List[AnomalyBehavior]:
        """根据严重等级获取异常行为"""
        return await self.find_by_conditions([
            AnomalyBehavior.default_severity == severity,
            AnomalyBehavior.is_enabled == True,
            AnomalyBehavior.deleted == False
        ])
    
    async def search_behaviors_by_keywords(self, keywords: List[str]) -> List[AnomalyBehavior]:
        """根据关键词搜索异常行为"""
        try:
            if not keywords:
                return []
            
            # 构建搜索条件：在AI关键词中包含任一搜索关键词
            conditions = []
            for keyword in keywords:
                conditions.append(
                    AnomalyBehavior.ai_keywords.contains(keyword)
                )
            
            query = self.db.query(AnomalyBehavior).filter(
                and_(
                    or_(*conditions),
                    AnomalyBehavior.is_enabled == True,
                    AnomalyBehavior.deleted == False
                )
            )
            
            return query.all()
            
        except Exception as e:
            logger.error(f"根据关键词搜索异常行为失败: {e}")
            return []
    
    async def get_behavior_config(self, behavior_id: int) -> Dict[str, Any]:
        """获取异常行为的完整配置信息"""
        try:
            behavior = await self.get_by_id(behavior_id)
            if not behavior:
                return {}
            
            return {
                "id": behavior.id,
                "code": behavior.code,
                "name": behavior.name,
                "category": behavior.category,
                "description": behavior.description,
                "ai_keywords": behavior.ai_keywords or [],
                "default_severity": behavior.default_severity,
                "default_confidence_threshold": float(behavior.default_confidence_threshold),
                "is_enabled": behavior.is_enabled,
                "sort_order": behavior.sort_order,
                "metadata": behavior.metadata or {}
            }
            
        except Exception as e:
            logger.error(f"获取行为 {behavior_id} 配置失败: {e}")
            return {}
    
    async def get_category_summary(self) -> Dict[str, Any]:
        """获取异常行为分类汇总"""
        try:
            behaviors = await self.get_enabled_behaviors()
            
            summary = {
                "total_count": len(behaviors),
                "category_breakdown": {},
                "severity_breakdown": {}
            }
            
            for behavior in behaviors:
                # 分类统计
                category = behavior.category
                if category not in summary["category_breakdown"]:
                    summary["category_breakdown"][category] = 0
                summary["category_breakdown"][category] += 1
                
                # 严重等级统计
                severity = behavior.default_severity
                if severity not in summary["severity_breakdown"]:
                    summary["severity_breakdown"][severity] = 0
                summary["severity_breakdown"][severity] += 1
            
            return summary
            
        except Exception as e:
            logger.error(f"获取异常行为分类汇总失败: {e}")
            return {}
    
    async def batch_update_keywords(self, behavior_updates: List[Dict[str, Any]]) -> int:
        """批量更新异常行为的AI关键词"""
        try:
            updated_count = 0
            
            for update in behavior_updates:
                behavior_id = update.get("behavior_id")
                new_keywords = update.get("ai_keywords", [])
                
                if behavior_id and new_keywords:
                    success = await self.update_by_id(behavior_id, {"ai_keywords": new_keywords})
                    if success:
                        updated_count += 1
            
            logger.info(f"批量更新异常行为关键词，成功更新 {updated_count} 个行为")
            return updated_count
            
        except Exception as e:
            logger.error(f"批量更新异常行为关键词失败: {e}")
            return 0 