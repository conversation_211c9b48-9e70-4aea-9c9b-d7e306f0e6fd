"""
数据库连接管理
"""
import logging
from typing import AsyncGenerator, Optional
from contextlib import asynccontextmanager

from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker
from sqlalchemy.exc import SQLAlchemyError

from .engine import get_engine
from app.core.exceptions import DatabaseException

logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self._session_factory: Optional[async_sessionmaker] = None
    
    async def initialize(self):
        """初始化数据库管理器"""
        if self._session_factory is None:
            engine = get_engine()
            self._session_factory = async_sessionmaker(
                engine,
                class_=AsyncSession,
                expire_on_commit=False,
                autoflush=True,
                autocommit=False,
            )
            logger.info("✅ 数据库会话工厂初始化完成")
    
    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """获取数据库会话"""
        if self._session_factory is None:
            await self.initialize()
        
        async with self._session_factory() as session:
            try:
                yield session
                await session.commit()
            except SQLAlchemyError as e:
                await session.rollback()
                logger.error(f"数据库操作异常: {str(e)}")
                raise DatabaseException(f"数据库操作失败: {str(e)}")
            except Exception as e:
                await session.rollback()
                logger.error(f"未知数据库异常: {str(e)}")
                raise DatabaseException(f"数据库操作异常: {str(e)}")
            finally:
                await session.close()
    
    async def check_connection(self) -> bool:
        """检查数据库连接"""
        try:
            async with self.get_session() as session:
                await session.execute("SELECT 1")
                return True
        except Exception as e:
            logger.error(f"数据库连接检查失败: {str(e)}")
            return False
    
    async def close(self):
        """关闭数据库管理器"""
        from .engine import close_engine
        await close_engine()
        self._session_factory = None
        logger.info("✅ 数据库连接已关闭")


# 全局数据库管理器实例
_db_manager: Optional[DatabaseManager] = None


async def get_database_manager() -> DatabaseManager:
    """获取数据库管理器实例"""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager()
        await _db_manager.initialize()
    return _db_manager


async def get_session() -> AsyncGenerator[AsyncSession, None]:
    """
    FastAPI依赖注入函数 - 获取数据库会话
    """
    db_manager = await get_database_manager()
    async with db_manager.get_session() as session:
        yield session


async def create_session() -> AsyncSession:
    """
    创建新的数据库会话 - 用于服务层
    注意：调用者负责关闭会话
    """
    db_manager = await get_database_manager()
    if db_manager._session_factory is None:
        await db_manager.initialize()

    session = db_manager._session_factory()
    return session