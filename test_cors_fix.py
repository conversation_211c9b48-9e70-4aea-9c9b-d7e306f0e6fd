#!/usr/bin/env python3
"""
测试CORS修复
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_cors_property():
    """测试CORS属性解析"""
    print("🔧 测试CORS属性解析...")
    
    try:
        from app.core.config import ServerSettings
        
        print("✅ ServerSettings导入成功")
        
        # 测试默认值
        server_settings = ServerSettings()
        print(f"✅ ServerSettings创建成功")
        print(f"内部字符串: {server_settings.cors_origins_str!r}")
        print(f"CORS Origins属性: {server_settings.cors_origins}")
        
        # 测试多个域名
        os.environ["CORS_ORIGINS"] = "http://localhost:3000,https://example.com"
        server_settings2 = ServerSettings()
        print(f"多域名测试: {server_settings2.cors_origins}")
        
        # 恢复环境变量
        os.environ["CORS_ORIGINS"] = "*"
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_settings():
    """测试主Settings类"""
    print("\n🔧 测试主Settings类...")
    
    try:
        from app.core.config import Settings
        
        print("✅ Settings导入成功")
        
        settings = Settings()
        print(f"✅ Settings创建成功")
        print(f"应用名称: {settings.app_name}")
        print(f"CORS Origins: {settings.server.cors_origins}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 测试CORS修复...")
    print("=" * 50)
    
    tests = [
        ("CORS属性解析", test_cors_property),
        ("主Settings类", test_main_settings),
    ]
    
    results = []
    for test_name, test_func in tests:
        result = test_func()
        results.append((test_name, result))
    
    print("=" * 50)
    print("📊 测试结果:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 CORS修复成功!")
        print("现在可以尝试启动系统: python start.py")
    else:
        print("\n💥 修复失败，需要进一步检查")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
