"""
日志配置模块
"""
import logging
import logging.config
import logging.handlers
import sys
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

from app.core.config import settings


class JsonFormatter(logging.Formatter):
    """JSON格式化器"""
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为JSON格式"""
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # 添加异常信息
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)
        
        # 添加额外字段
        for key, value in record.__dict__.items():
            if key not in [
                "name", "msg", "args", "levelname", "levelno", "pathname",
                "filename", "module", "exc_info", "exc_text", "stack_info",
                "lineno", "funcName", "created", "msecs", "relativeCreated",
                "thread", "threadName", "processName", "process", "getMessage"
            ]:
                log_data[key] = value
        
        return json.dumps(log_data, ensure_ascii=False, default=str)


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',      # 青色
        'INFO': '\033[32m',       # 绿色
        'WARNING': '\033[33m',    # 黄色
        'ERROR': '\033[31m',      # 红色
        'CRITICAL': '\033[35m',   # 紫色
        'RESET': '\033[0m'        # 重置
    }
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为彩色输出"""
        # 获取颜色
        color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        reset = self.COLORS['RESET']
        
        # 格式化时间
        timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S')
        
        # 构建日志消息
        log_message = (
            f"{color}[{timestamp}] {record.levelname:8} "
            f"{record.name}:{record.lineno} - {record.getMessage()}{reset}"
        )
        
        # 添加异常信息
        if record.exc_info:
            log_message += f"\n{self.formatException(record.exc_info)}"
        
        return log_message


def create_file_handler(
    filename: str,
    level: int = logging.INFO,
    max_bytes: int = 10485760,  # 10MB
    backup_count: int = 5,
    encoding: str = "utf-8"
) -> logging.handlers.RotatingFileHandler:
    """创建文件处理器"""
    # 确保日志目录存在
    log_path = Path(filename)
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    handler = logging.handlers.RotatingFileHandler(
        filename=filename,
        maxBytes=max_bytes,
        backupCount=backup_count,
        encoding=encoding
    )
    handler.setLevel(level)
    
    # 设置格式化器
    if settings.logging.enable_json_format:
        handler.setFormatter(JsonFormatter())
    else:
        formatter = logging.Formatter(
            fmt=settings.logging.format,
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
    
    return handler


def create_console_handler(level: int = logging.INFO) -> logging.StreamHandler:
    """创建控制台处理器"""
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(level)
    
    # 设置格式化器
    if settings.logging.enable_json_format:
        handler.setFormatter(JsonFormatter())
    else:
        # 开发环境使用彩色输出
        if settings.is_development:
            handler.setFormatter(ColoredFormatter())
        else:
            formatter = logging.Formatter(
                fmt=settings.logging.format,
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            handler.setFormatter(formatter)
    
    return handler


def setup_logging() -> None:
    """设置日志配置"""
    # 获取日志级别
    level = getattr(logging, settings.logging.level.upper(), logging.INFO)
    
    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # 清除现有处理器
    root_logger.handlers.clear()
    
    # 添加文件处理器
    if settings.logging.enable_file_logging:
        file_handler = create_file_handler(
            filename=settings.logging.file_path,
            level=level,
            max_bytes=settings.logging.max_bytes,
            backup_count=settings.logging.backup_count
        )
        root_logger.addHandler(file_handler)
    
    # 添加控制台处理器
    if settings.logging.enable_console_logging:
        console_handler = create_console_handler(level=level)
        root_logger.addHandler(console_handler)
    
    # 配置第三方库的日志级别
    configure_third_party_loggers()
    
    # 记录配置信息
    logger = logging.getLogger(__name__)
    logger.info(f"日志系统初始化完成 - 级别: {settings.logging.level}")


def configure_third_party_loggers() -> None:
    """配置第三方库的日志级别"""
    # 设置第三方库日志级别
    third_party_loggers = {
        'uvicorn': logging.INFO,
        'uvicorn.error': logging.INFO,
        'uvicorn.access': logging.WARNING if settings.is_production else logging.INFO,
        'fastapi': logging.INFO,
        'sqlalchemy': logging.WARNING,
        'sqlalchemy.engine': logging.WARNING,
        'aiomysql': logging.WARNING,
        'aioredis': logging.WARNING,
        'httpx': logging.WARNING,
        'asyncio': logging.WARNING,
    }
    
    for logger_name, level in third_party_loggers.items():
        logging.getLogger(logger_name).setLevel(level)


def get_logger(name: str) -> logging.Logger:
    """获取日志器"""
    return logging.getLogger(name)


class LoggerMixin:
    """日志器混入类"""
    
    @property
    def logger(self) -> logging.Logger:
        """获取当前类的日志器"""
        return logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")


def log_execution_time(func_name: str = None):
    """记录函数执行时间的装饰器"""
    def decorator(func):
        import functools
        import time
        
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            logger = logging.getLogger(func.__module__)
            
            try:
                result = await func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                logger.debug(
                    f"⏱️ {func_name or func.__name__} 执行完成 - 耗时: {execution_time:.3f}s"
                )
                
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(
                    f"❌ {func_name or func.__name__} 执行失败 - 耗时: {execution_time:.3f}s - 错误: {str(e)}"
                )
                raise
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            logger = logging.getLogger(func.__module__)
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                logger.debug(
                    f"⏱️ {func_name or func.__name__} 执行完成 - 耗时: {execution_time:.3f}s"
                )
                
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(
                    f"❌ {func_name or func.__name__} 执行失败 - 耗时: {execution_time:.3f}s - 错误: {str(e)}"
                )
                raise
        
        # 根据函数类型返回对应的包装器
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


# 便捷的日志记录函数
def log_info(message: str, logger_name: str = None, **kwargs):
    """记录信息日志"""
    logger = logging.getLogger(logger_name or __name__)
    logger.info(message, extra=kwargs)


def log_warning(message: str, logger_name: str = None, **kwargs):
    """记录警告日志"""
    logger = logging.getLogger(logger_name or __name__)
    logger.warning(message, extra=kwargs)


def log_error(message: str, logger_name: str = None, exc_info: bool = False, **kwargs):
    """记录错误日志"""
    logger = logging.getLogger(logger_name or __name__)
    logger.error(message, exc_info=exc_info, extra=kwargs)


def log_debug(message: str, logger_name: str = None, **kwargs):
    """记录调试日志"""
    logger = logging.getLogger(logger_name or __name__)
    logger.debug(message, extra=kwargs)


# 默认日志器，用于向后兼容
logger = logging.getLogger(__name__) 