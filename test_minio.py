#!/usr/bin/env python3
"""
MinIO连接测试脚本
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_minio_connection():
    """测试MinIO连接"""
    print("🔧 测试MinIO连接...")
    
    try:
        from minio import Minio
        from minio.error import S3Error
        
        # 从环境变量读取配置
        endpoint = os.getenv("MINIO_ENDPOINT", "124.71.23.144:9000")
        access_key = os.getenv("MINIO_ACCESS_KEY", "v3Di4s9aszZC6DzeNqHa")
        secret_key = os.getenv("MINIO_SECRET_KEY", "YAXnJlXazQsjPvsJhZk9ow3qRfvl8Gglu7P8OpHJ")
        secure = os.getenv("MINIO_SECURE", "false").lower() == "true"
        bucket_name = os.getenv("MINIO_BUCKET_NAME", "difybn")
        
        print(f"📊 MinIO配置:")
        print(f"  端点: {endpoint}")
        print(f"  访问密钥: {access_key[:8]}...")
        print(f"  安全连接: {secure}")
        print(f"  存储桶: {bucket_name}")
        
        # 创建MinIO客户端
        print("\n🔌 创建MinIO客户端...")
        client = Minio(
            endpoint,
            access_key=access_key,
            secret_key=secret_key,
            secure=secure
        )
        print("✅ MinIO客户端创建成功")
        
        # 测试连接 - 列出存储桶
        print("\n📋 测试连接 - 列出存储桶...")
        buckets = client.list_buckets()
        print("✅ 连接成功! 可用存储桶:")
        for bucket in buckets:
            print(f"  - {bucket.name} (创建时间: {bucket.creation_date})")
        
        # 检查目标存储桶是否存在
        print(f"\n🔍 检查存储桶 '{bucket_name}' 是否存在...")
        if client.bucket_exists(bucket_name):
            print(f"✅ 存储桶 '{bucket_name}' 存在")
        else:
            print(f"❌ 存储桶 '{bucket_name}' 不存在")
            
            # 尝试创建存储桶
            print(f"🔨 尝试创建存储桶 '{bucket_name}'...")
            try:
                client.make_bucket(bucket_name)
                print(f"✅ 存储桶 '{bucket_name}' 创建成功")
            except S3Error as e:
                print(f"❌ 创建存储桶失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ MinIO连接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_alternative_configs():
    """测试替代配置"""
    print("\n🔧 测试替代配置...")
    
    # 测试不同的端点格式
    alternative_configs = [
        {
            "name": "带HTTP前缀",
            "endpoint": "http://124.71.23.144:9000",
            "secure": False
        },
        {
            "name": "不同端口",
            "endpoint": "124.71.23.144:9001",
            "secure": False
        }
    ]
    
    for config in alternative_configs:
        print(f"\n📋 测试配置: {config['name']}")
        print(f"  端点: {config['endpoint']}")
        
        try:
            from minio import Minio
            
            client = Minio(
                config["endpoint"],
                access_key=os.getenv("MINIO_ACCESS_KEY", "v3Di4s9aszZC6DzeNqHa"),
                secret_key=os.getenv("MINIO_SECRET_KEY", "YAXnJlXazQsjPvsJhZk9ow3qRfvl8Gglu7P8OpHJ"),
                secure=config["secure"]
            )
            
            # 尝试列出存储桶
            buckets = client.list_buckets()
            print(f"✅ 配置 '{config['name']}' 连接成功!")
            return True
            
        except Exception as e:
            print(f"❌ 配置 '{config['name']}' 连接失败: {e}")
    
    return False

def suggest_solutions():
    """建议解决方案"""
    print("\n💡 建议解决方案:")
    print("1. 检查MinIO服务器是否正常运行")
    print("2. 验证访问密钥和秘密密钥是否正确")
    print("3. 确认端点地址和端口是否正确")
    print("4. 检查网络连接是否正常")
    print("5. 考虑使用本地MinIO服务器进行测试")
    print("\n🔧 临时解决方案:")
    print("- 可以在配置中禁用MinIO存储，使用本地文件存储")
    print("- 或者配置一个可用的MinIO服务器")

def main():
    """主函数"""
    print("🚀 MinIO连接诊断...")
    print("=" * 60)
    
    # 加载环境变量
    from dotenv import load_dotenv
    load_dotenv()
    
    success = test_minio_connection()
    
    if not success:
        success = test_alternative_configs()
    
    if not success:
        suggest_solutions()
    
    print("=" * 60)
    if success:
        print("🎉 MinIO连接测试成功!")
    else:
        print("💥 MinIO连接测试失败")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
