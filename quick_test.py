#!/usr/bin/env python3
"""
快速测试修复
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def main():
    """快速测试"""
    print("🚀 快速测试配置修复...")
    
    try:
        # 测试ServerSettings
        from app.core.config import ServerSettings
        print("✅ ServerSettings导入成功")
        
        server_settings = ServerSettings()
        print(f"✅ ServerSettings创建成功")
        print(f"CORS Origins: {server_settings.cors_origins}")
        
        # 测试主Settings
        from app.core.config import Settings
        print("✅ Settings导入成功")
        
        settings = Settings()
        print(f"✅ Settings创建成功")
        print(f"应用名称: {settings.app_name}")
        
        # 测试主模块导入
        import app.main
        print("✅ 主模块导入成功")
        
        print("\n🎉 所有测试通过! 现在可以启动系统了!")
        print("运行: python start.py")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
