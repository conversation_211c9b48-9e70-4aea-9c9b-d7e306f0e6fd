#!/usr/bin/env python3
"""
测试AlertService修复
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_alert_service():
    """测试AlertService初始化"""
    print("🔧 测试AlertService初始化...")
    
    try:
        # 加载环境变量
        from dotenv import load_dotenv
        load_dotenv()
        
        # 获取数据库会话
        from app.database import get_session
        db_session = await get_session()
        print("✅ 数据库会话获取成功")
        
        # 测试AlertService初始化
        from app.services.alert.alert_service import AlertService
        from app.services.storage.file_storage_service import FileStorageService
        from app.services.notification.websocket_service import WebSocketService
        
        storage_service = FileStorageService()
        websocket_service = WebSocketService()
        
        alert_service = AlertService(
            db_session=db_session,
            storage_service=storage_service,
            websocket_service=websocket_service
        )
        print("✅ AlertService初始化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ AlertService测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_main_startup():
    """测试主程序启动"""
    print("\n🔧 测试主程序启动...")
    
    try:
        # 测试主模块导入
        import app.main
        print("✅ 主模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 主程序测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🚀 测试AlertService修复...")
    print("=" * 50)
    
    tests = [
        ("AlertService初始化", test_alert_service),
        ("主程序启动", test_main_startup),
    ]
    
    results = []
    for test_name, test_func in tests:
        result = await test_func()
        results.append((test_name, result))
    
    print("=" * 50)
    print("📊 测试结果:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 AlertService修复成功!")
        print("现在可以启动系统: python start.py")
    else:
        print("\n💥 修复失败，需要进一步检查")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    import asyncio
    sys.exit(asyncio.run(main()))
