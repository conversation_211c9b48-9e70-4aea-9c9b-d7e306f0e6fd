#!/usr/bin/env python3
"""
诊断配置问题
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def check_versions():
    """检查版本信息"""
    print("📦 检查版本信息...")
    
    try:
        import pydantic
        print(f"Pydantic版本: {pydantic.__version__}")
        
        import pydantic_settings
        print(f"Pydantic Settings版本: {pydantic_settings.__version__}")
        
        print(f"Python版本: {sys.version}")
        
    except Exception as e:
        print(f"❌ 版本检查失败: {e}")

def check_env_file():
    """检查环境文件"""
    print("\n📄 检查环境文件...")
    
    env_file = Path(".env")
    if env_file.exists():
        print(f"✅ .env文件存在")
        
        # 读取文件内容
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找CORS_ORIGINS
            for line_num, line in enumerate(content.splitlines(), 1):
                if line.strip().startswith("CORS_ORIGINS"):
                    print(f"第{line_num}行: {line!r}")
                    
        except Exception as e:
            print(f"❌ 读取.env文件失败: {e}")
    else:
        print(f"❌ .env文件不存在")

def test_direct_import():
    """直接测试导入"""
    print("\n🔧 测试直接导入...")
    
    try:
        # 设置环境变量
        os.environ["CORS_ORIGINS"] = "*"
        
        from app.core.config import ServerSettings
        
        print("✅ ServerSettings导入成功")
        
        # 尝试创建实例
        server_settings = ServerSettings()
        print(f"✅ ServerSettings实例创建成功")
        print(f"CORS Origins: {server_settings.cors_origins}")
        
    except Exception as e:
        print(f"❌ 直接导入测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_settings_class():
    """测试主Settings类"""
    print("\n🔧 测试主Settings类...")
    
    try:
        from app.core.config import Settings
        
        print("✅ Settings导入成功")
        
        # 尝试创建实例
        settings = Settings()
        print(f"✅ Settings实例创建成功")
        print(f"应用名称: {settings.app_name}")
        print(f"CORS Origins: {settings.server.cors_origins}")
        
    except Exception as e:
        print(f"❌ Settings测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔍 开始诊断配置问题...")
    print("=" * 60)
    
    check_versions()
    check_env_file()
    test_direct_import()
    test_settings_class()
    
    print("=" * 60)
    print("🏁 诊断完成")

if __name__ == "__main__":
    main()
