"""
门店数据访问层
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_

from ..database.base_repository import BaseRepository
from ..models.store import Store
from ..core.logger import logger


class StoreRepository(BaseRepository[Store]):
    """门店Repository"""
    
    def __init__(self, db: Session):
        super().__init__(Store, db)
    
    async def get_store_by_code(self, code: str) -> Optional[Store]:
        """根据门店编号获取门店"""
        return await self.find_by_field("code", code)
    
    async def get_active_stores(self) -> List[Store]:
        """获取所有正常状态的门店"""
        return await self.find_by_conditions([
            Store.status.in_(["normal", "warning"]),
            Store.deleted == False
        ])
    
    async def get_stores_by_district(self, district: str) -> List[Store]:
        """根据区域获取门店"""
        return await self.find_by_conditions([
            Store.district == district,
            Store.deleted == False
        ])
    
    async def update_camera_count(self, store_id: int, total_count: int, online_count: int) -> bool:
        """更新门店摄像头统计"""
        try:
            update_data = {
                "camera_count": total_count,
                "online_camera_count": online_count
            }
            success = await self.update_by_id(store_id, update_data)
            if success:
                logger.debug(f"门店 {store_id} 摄像头统计更新: 总数={total_count}, 在线={online_count}")
            return success
            
        except Exception as e:
            logger.error(f"更新门店 {store_id} 摄像头统计失败: {e}")
            return False 