2025-07-01 23:57:15,874 - __main__ - ERROR - 启动失败: `BaseSettings` has been moved to the `pydantic-settings` package. See https://docs.pydantic.dev/2.5/migration/#basesettings-has-moved-to-pydantic-settings for more details.

For further information visit https://errors.pydantic.dev/2.5/u/import-error
2025-07-02 00:01:58,805 - __main__ - ERROR - 启动失败: 65 validation errors for Settings
debug
  Extra inputs are not permitted [type=extra_forbidden, input_value='true', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
project_name
  Extra inputs are not permitted [type=extra_forbidden, input_value='智能视频监控预警系统', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
version
  Extra inputs are not permitted [type=extra_forbidden, input_value='1.0.0', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
host
  Extra inputs are not permitted [type=extra_forbidden, input_value='0.0.0.0', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
port
  Extra inputs are not permitted [type=extra_forbidden, input_value='8000', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
reload
  Extra inputs are not permitted [type=extra_forbidden, input_value='true', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
db_host
  Extra inputs are not permitted [type=extra_forbidden, input_value='localhost', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
db_port
  Extra inputs are not permitted [type=extra_forbidden, input_value='3306', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
db_user
  Extra inputs are not permitted [type=extra_forbidden, input_value='root', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
db_password
  Extra inputs are not permitted [type=extra_forbidden, input_value='Aa123456', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
db_name
  Extra inputs are not permitted [type=extra_forbidden, input_value='video_monitor_v1', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
db_pool_size
  Extra inputs are not permitted [type=extra_forbidden, input_value='20', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
db_max_overflow
  Extra inputs are not permitted [type=extra_forbidden, input_value='30', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
db_pool_timeout
  Extra inputs are not permitted [type=extra_forbidden, input_value='30', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
db_pool_recycle
  Extra inputs are not permitted [type=extra_forbidden, input_value='3600', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
db_echo
  Extra inputs are not permitted [type=extra_forbidden, input_value='false', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
redis_host
  Extra inputs are not permitted [type=extra_forbidden, input_value='localhost', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
redis_port
  Extra inputs are not permitted [type=extra_forbidden, input_value='6379', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
redis_password
  Extra inputs are not permitted [type=extra_forbidden, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
redis_db
  Extra inputs are not permitted [type=extra_forbidden, input_value='0', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
redis_pool_size
  Extra inputs are not permitted [type=extra_forbidden, input_value='20', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
redis_timeout
  Extra inputs are not permitted [type=extra_forbidden, input_value='5', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
minio_endpoint
  Extra inputs are not permitted [type=extra_forbidden, input_value='http://124.71.23.144:9000', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
minio_access_key
  Extra inputs are not permitted [type=extra_forbidden, input_value='v3Di4s9aszZC6DzeNqHa', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
minio_secret_key
  Extra inputs are not permitted [type=extra_forbidden, input_value='YAXnJlXazQsjPvsJhZk9ow3qRfvl8Gglu7P8OpHJ', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
minio_secure
  Extra inputs are not permitted [type=extra_forbidden, input_value='false', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
minio_bucket_name
  Extra inputs are not permitted [type=extra_forbidden, input_value='difybn', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
ai_api_key
  Extra inputs are not permitted [type=extra_forbidden, input_value='sk-11dd2a8ef30445ac8b4fc3cdcef944e8', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
ai_model_name
  Extra inputs are not permitted [type=extra_forbidden, input_value='qwen-omni-turbo', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
ai_max_retries
  Extra inputs are not permitted [type=extra_forbidden, input_value='3', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
ai_timeout
  Extra inputs are not permitted [type=extra_forbidden, input_value='30', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
ai_max_concurrent
  Extra inputs are not permitted [type=extra_forbidden, input_value='50', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
video_max_streams
  Extra inputs are not permitted [type=extra_forbidden, input_value='100', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
video_frame_rate
  Extra inputs are not permitted [type=extra_forbidden, input_value='2', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
video_buffer_size
  Extra inputs are not permitted [type=extra_forbidden, input_value='30', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
video_timeout
  Extra inputs are not permitted [type=extra_forbidden, input_value='30', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
video_reconnect_interval
  Extra inputs are not permitted [type=extra_forbidden, input_value='5', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
video_max_reconnects
  Extra inputs are not permitted [type=extra_forbidden, input_value='3', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
motion_detection_threshold
  Extra inputs are not permitted [type=extra_forbidden, input_value='5000', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
motion_detection_blur_size
  Extra inputs are not permitted [type=extra_forbidden, input_value='15', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
motion_detection_min_area
  Extra inputs are not permitted [type=extra_forbidden, input_value='500', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
frame_aggregation_window
  Extra inputs are not permitted [type=extra_forbidden, input_value='5', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
frame_aggregation_max_frames
  Extra inputs are not permitted [type=extra_forbidden, input_value='10', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
batch_processing_size
  Extra inputs are not permitted [type=extra_forbidden, input_value='5', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
batch_processing_timeout
  Extra inputs are not permitted [type=extra_forbidden, input_value='10', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
alert_buffer_size
  Extra inputs are not permitted [type=extra_forbidden, input_value='1000', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
alert_batch_size
  Extra inputs are not permitted [type=extra_forbidden, input_value='50', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
alert_flush_interval
  Extra inputs are not permitted [type=extra_forbidden, input_value='5', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
websocket_max_connections
  Extra inputs are not permitted [type=extra_forbidden, input_value='1000', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
websocket_heartbeat_interval
  Extra inputs are not permitted [type=extra_forbidden, input_value='30', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
websocket_reconnect_interval
  Extra inputs are not permitted [type=extra_forbidden, input_value='5', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
log_level
  Extra inputs are not permitted [type=extra_forbidden, input_value='INFO', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
log_format
  Extra inputs are not permitted [type=extra_forbidden, input_value='json', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
log_file_path
  Extra inputs are not permitted [type=extra_forbidden, input_value='logs/app.log', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
log_max_size
  Extra inputs are not permitted [type=extra_forbidden, input_value='100MB', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
log_backup_count
  Extra inputs are not permitted [type=extra_forbidden, input_value='10', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
log_rotation
  Extra inputs are not permitted [type=extra_forbidden, input_value='1 day', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
secret_key
  Extra inputs are not permitted [type=extra_forbidden, input_value='your-secret-key-here', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
access_token_expire_minutes
  Extra inputs are not permitted [type=extra_forbidden, input_value='1440', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
cors_origins
  Extra inputs are not permitted [type=extra_forbidden, input_value='*', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
resource_monitor_interval
  Extra inputs are not permitted [type=extra_forbidden, input_value='60', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
metric_collection_enabled
  Extra inputs are not permitted [type=extra_forbidden, input_value='true', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
enable_swagger
  Extra inputs are not permitted [type=extra_forbidden, input_value='true', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
enable_redoc
  Extra inputs are not permitted [type=extra_forbidden, input_value='true', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
enable_debug_endpoints
  Extra inputs are not permitted [type=extra_forbidden, input_value='true', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/extra_forbidden
2025-07-21 11:25:11,928 - __main__ - ERROR - 启动失败: error parsing value for field "cors_origins" from source "EnvSettingsSource"
